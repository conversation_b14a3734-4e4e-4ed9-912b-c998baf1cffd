import { Metadata } from 'next';
import { NewsPage } from "@/features/news/pages";

/**
 * News Route - Next.js App Router Page
 *
 * This page serves as the routing layer for the news page.
 * The actual implementation is handled by the NewsPage component
 * from the news feature, following the Hybrid Feature-Module Architecture.
 *
 * @see src/features/news/pages/news-page/v1/NewsPageV1.tsx
 */

// SEO Metadata for News Page - Optimized for Search Engines
export const metadata: Metadata = {
  title: 'Latest Sports News | Breaking Football News & Updates',
  description: 'Stay updated with the latest sports news, breaking football stories, transfer updates, match reports, and exclusive interviews. Your ultimate source for sports journalism.',
  keywords: [
    'sports news',
    'football news',
    'breaking news',
    'transfer news',
    'match reports',
    'sports updates',
    'football transfers',
    'sports journalism',
    'latest sports',
    'football stories'
  ],
  authors: [{ name: 'Sports News Team' }],
  creator: 'Sports News Platform',
  publisher: 'Sports News Platform',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_FE_DOMAIN || 'http://localhost:5000'),
  alternates: {
    canonical: '/news',
  },
  openGraph: {
    title: 'Latest Sports News | Breaking Football News & Updates',
    description: 'Stay updated with the latest sports news, breaking football stories, transfer updates, match reports, and exclusive interviews.',
    url: '/news',
    siteName: 'Sports News Platform',
    images: [
      {
        url: '/images/og/news-page.jpg',
        width: 1200,
        height: 630,
        alt: 'Latest Sports News',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Latest Sports News | Breaking Football News & Updates',
    description: 'Stay updated with the latest sports news, breaking football stories, transfer updates, match reports, and exclusive interviews.',
    images: ['/images/og/news-page.jpg'],
    creator: '@sportsnews',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
  },
};

interface NewsPageProps {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}

export default async function News({ searchParams }: NewsPageProps) {
  const resolvedSearchParams = await searchParams;

  return (
    <NewsPage />
  );
}
