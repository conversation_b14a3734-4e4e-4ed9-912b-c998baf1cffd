import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { NewsDetailPage } from "@/features/news/pages";
import { NewsService } from '@/features/news/services/NewsService';
import { NewsArticle } from '@/features/news/types';

interface NewsDetailPageProps {
  params: Promise<{ slug: string }>;
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}

/**
 * News Detail Route - Next.js App Router Page
 *
 * This page serves individual news articles.
 * URL Structure: /news/{slug}
 *
 * @see src/features/news/pages/news-detail-page/v2/NewsDetailPageV2.tsx
 */

// Generate metadata for SEO
export async function generateMetadata({ params }: NewsDetailPageProps): Promise<Metadata> {
  try {
    const { slug } = await params;
    const newsService = new NewsService();
    const article = await newsService.getArticleBySlug(slug);

    if (!article) {
      return {
        title: 'Article Not Found',
        description: 'The requested article could not be found.',
      };
    }

    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:5000';
    const articleUrl = `${baseUrl}/news/${article.slug}`;
    const imageUrl = article.featuredImage?.url 
      ? `${process.env.NEXT_PUBLIC_CDN_DOMAIN_PICTURE}${article.featuredImage.url}`
      : `${baseUrl}/images/default-news.jpg`;

    return {
      title: `${article.title} | Sports News`,
      description: article.excerpt || article.metaDescription || `Read the latest sports news: ${article.title}`,
      keywords: [
        article.category.name,
        'sports news',
        'football',
        'soccer',
        ...(article.tags || [])
      ].join(', '),
      authors: [{ name: article.author.name }],
      openGraph: {
        title: article.title,
        description: article.excerpt || article.metaDescription || '',
        url: articleUrl,
        siteName: 'Sports News',
        images: [
          {
            url: imageUrl,
            width: 1200,
            height: 630,
            alt: article.title,
          }
        ],
        locale: 'vi_VN',
        type: 'article',
        publishedTime: article.publishedAt,
        modifiedTime: article.updatedAt,
        section: article.category.name,
        tags: article.tags || [],
      },
      twitter: {
        card: 'summary_large_image',
        title: article.title,
        description: article.excerpt || article.metaDescription || '',
        images: [imageUrl],
        creator: `@${article.author.name.replace(/\s+/g, '').toLowerCase()}`,
      },
      alternates: {
        canonical: articleUrl,
      },
      robots: {
        index: article.status === 'published',
        follow: true,
        googleBot: {
          index: article.status === 'published',
          follow: true,
          'max-video-preview': -1,
          'max-image-preview': 'large',
          'max-snippet': -1,
        },
      },
      other: {
        'article:author': article.author.name,
        'article:published_time': article.publishedAt,
        'article:modified_time': article.updatedAt,
        'article:section': article.category.name,
        'article:tag': (article.tags || []).join(','),
      },
    };
  } catch (error) {
    console.error('Error generating metadata for news article:', error);
    return {
      title: 'Sports News Article',
      description: 'Read the latest sports news and updates.',
    };
  }
}

// Generate static params for better performance (optional)
export async function generateStaticParams() {
  try {
    const newsService = new NewsService();
    // Get recent articles for static generation
    const response = await newsService.getArticles({}, 'publishedAt_desc', 1);
    
    return response.data.slice(0, 50).map((article) => ({
      slug: article.slug,
    }));
  } catch (error) {
    console.warn('Failed to generate static params for news articles:', error);
    return [];
  }
}

export default async function NewsDetailRoute({ params, searchParams }: NewsDetailPageProps) {
  try {
    const { slug } = await params;
    const resolvedSearchParams = await searchParams;
    
    // Fetch article data
    const newsService = new NewsService();
    const article = await newsService.getArticleBySlug(slug);

    if (!article) {
      notFound();
    }

    // Ensure article is published
    if (article.status !== 'published') {
      notFound();
    }

    return (
      <NewsDetailPage 
        article={article}
        searchParams={resolvedSearchParams}
      />
    );
  } catch (error) {
    console.error('Error loading news article:', error);
    notFound();
  }
}
