import { Metadata } from 'next';
import { NewsDetailPage } from "@/features/news/pages";

interface NewsDetailPageProps {
  params: Promise<{ slug: string }>;
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}

/**
 * News Detail Route - Next.js App Router Page
 *
 * This page serves individual news articles.
 * URL Structure: /news/{slug}
 *
 * @see src/features/news/pages/news-detail-page/v2/NewsDetailPageV2.tsx
 */

// Generate metadata for SEO
export async function generateMetadata({ params }: NewsDetailPageProps): Promise<Metadata> {
  try {
    const { slug } = await params;

    // Try to fetch article data from API for metadata
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:5000';
    const response = await fetch(`${baseUrl}/api/news/article/${slug}`, {
      next: { revalidate: 300 }
    });

    if (!response.ok) {
      return {
        title: 'Article Not Found',
        description: 'The requested article could not be found.',
      };
    }

    const data = await response.json();
    const article = data.article;

    if (!article) {
      return {
        title: 'Article Not Found',
        description: 'The requested article could not be found.',
      };
    }

    const articleUrl = `${baseUrl}/news/${article.slug}`;
    const imageUrl = article.featuredImage || `${baseUrl}/images/default-news.jpg`;

    return {
      title: `${article.title} | Sports News`,
      description: article.excerpt || article.metaDescription || `Read the latest sports news: ${article.title}`,
      keywords: [
        article.category?.name || 'sports',
        'sports news',
        'football',
        'soccer',
        ...(article.tags || [])
      ].join(', '),
      authors: [{ name: article.author?.name || 'Sports News Team' }],
      openGraph: {
        title: article.title,
        description: article.excerpt || article.metaDescription || '',
        url: articleUrl,
        siteName: 'Sports News',
        images: [
          {
            url: imageUrl,
            width: 1200,
            height: 630,
            alt: article.title,
          }
        ],
        locale: 'vi_VN',
        type: 'article',
        publishedTime: article.publishedAt,
        modifiedTime: article.updatedAt,
        section: article.category?.name || 'Sports',
        tags: article.tags || [],
      },
      twitter: {
        card: 'summary_large_image',
        title: article.title,
        description: article.excerpt || article.metaDescription || '',
        images: [imageUrl],
      },
      alternates: {
        canonical: articleUrl,
      },
      robots: {
        index: article.status === 'published',
        follow: true,
        googleBot: {
          index: article.status === 'published',
          follow: true,
          'max-video-preview': -1,
          'max-image-preview': 'large',
          'max-snippet': -1,
        },
      },
    };
  } catch (error) {
    console.error('Error generating metadata for news article:', error);
    return {
      title: 'Sports News Article',
      description: 'Read the latest sports news and updates.',
    };
  }
}

// Generate static params for better performance (optional)
export async function generateStaticParams() {
  try {
    // Get recent articles for static generation
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:5000';
    const response = await fetch(`${baseUrl}/api/news?page=1&limit=50`, {
      next: { revalidate: 3600 }
    });

    if (!response.ok) {
      return [];
    }

    const data = await response.json();

    return data.data?.slice(0, 50).map((article: any) => ({
      slug: article.slug,
    })) || [];
  } catch (error) {
    console.warn('Failed to generate static params for news articles:', error);
    return [];
  }
}

export default async function NewsDetailRoute({ params, searchParams }: NewsDetailPageProps) {
  const { slug } = await params;
  const resolvedSearchParams = await searchParams;

  return (
    <NewsDetailPage />
  );
}
