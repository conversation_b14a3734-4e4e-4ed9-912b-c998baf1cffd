import { Metadata } from 'next';
import { NewsCategoryPage } from '@/features/news/pages';

interface NewsCategoryPageProps {
  params: { slug: string };
  searchParams: { [key: string]: string | string[] | undefined };
}

// Generate metadata for SEO
export async function generateMetadata({ params }: NewsCategoryPageProps): Promise<Metadata> {
  const { slug } = await params;

  // Convert slug to readable name
  const categoryName = slug.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

  return {
    title: `${categoryName} - <PERSON> tức thể thao`,
    description: `Tin tức ${categoryName.toLowerCase()} mới nhất và cập nhật liên tục`,
    openGraph: {
      title: `${categoryName} - Tin tức thể thao`,
      description: `Tin tức ${categoryName.toLowerCase()} mới nhất và cập nhật liên tục`,
      type: 'website',
    },
  };
}

export default async function NewsCategoryPageRoute({ params, searchParams }: NewsCategoryPageProps) {
  const { slug } = await params;

  // Pass category slug to NewsCategoryPage
  return (
    <NewsCategoryPage />
  );
}
