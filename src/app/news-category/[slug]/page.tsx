import { Metadata } from 'next';
import { NewsListPage } from '@/features/news/pages/NewsListPage';

interface NewsCategoryPageProps {
  params: { slug: string };
  searchParams: { [key: string]: string | string[] | undefined };
}

// Generate metadata for SEO
export async function generateMetadata({ params }: NewsCategoryPageProps): Promise<Metadata> {
  const { slug } = await params;
  
  // Convert slug to readable name
  const categoryName = slug.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  
  return {
    title: `${categoryName} - <PERSON> tức thể thao`,
    description: `Tin tức ${categoryName.toLowerCase()} mới nhất và cập nhật liên tục`,
    openGraph: {
      title: `${categoryName} - Tin tức thể thao`,
      description: `Tin tức ${categoryName.toLowerCase()} mới nhất và cập nhật liên tụ<PERSON>`,
      type: 'website',
    },
  };
}

export default async function NewsCategoryPage({ params, searchParams }: NewsCategoryPageProps) {
  const { slug } = await params;
  
  // Pass category slug to NewsListPage
  return (
    <NewsListPage 
      initialCategory={slug}
      searchParams={searchParams}
    />
  );
}
