// News Categories API Route - Proxy to backend with fallback data
import { NextRequest, NextResponse } from 'next/server';

// Mock categories data for development/fallback
const MOCK_CATEGORIES = [
  {
    id: '1',
    name: '<PERSON><PERSON><PERSON> đá',
    slug: 'bong-da',
    description: 'Tin tức bóng đá trong nước và quốc tế',
    icon: 'soccer',
    color: '#10B981',
    count: 25,
    sortOrder: 1,
    isActive: true,
    isPublic: true,
    metaTitle: 'Tin tức bóng đá',
    metaDescription: 'Cập nhật tin tức bóng đá mới nhất',
    articleCount: 25,
    publishedArticleCount: 23
  },
  {
    id: '2',
    name: '<PERSON><PERSON>g rổ',
    slug: 'bong-ro',
    description: 'Tin tức bóng rổ NBA, VBA và các giải đấu khác',
    icon: 'basketball',
    color: '#F59E0B',
    count: 18,
    sortOrder: 2,
    isActive: true,
    isPublic: true,
    metaTitle: 'Tin tức bóng rổ',
    metaDescription: 'Cậ<PERSON> nhật tin tức bóng rổ mới nhất',
    articleCount: 18,
    publishedArticleCount: 16
  },
  {
    id: '3',
    name: 'Tennis',
    slug: 'tennis',
    description: 'Tin tức tennis Grand Slam và các giải đấu lớn',
    icon: 'tennis',
    color: '#8B5CF6',
    count: 12,
    sortOrder: 3,
    isActive: true,
    isPublic: true,
    metaTitle: 'Tin tức tennis',
    metaDescription: 'Cập nhật tin tức tennis mới nhất',
    articleCount: 12,
    publishedArticleCount: 11
  },
  {
    id: '4',
    name: 'Thể thao khác',
    slug: 'the-thao-khac',
    description: 'Tin tức các môn thể thao khác',
    icon: 'sports',
    color: '#EF4444',
    count: 8,
    sortOrder: 4,
    isActive: true,
    isPublic: true,
    metaTitle: 'Tin tức thể thao',
    metaDescription: 'Cập nhật tin tức thể thao đa dạng',
    articleCount: 8,
    publishedArticleCount: 7
  },
  {
    id: '5',
    name: 'Chuyển nhượng',
    slug: 'chuyen-nhuong',
    description: 'Tin tức chuyển nhượng cầu thủ',
    icon: 'transfer',
    color: '#06B6D4',
    count: 15,
    sortOrder: 5,
    isActive: true,
    isPublic: true,
    metaTitle: 'Tin chuyển nhượng',
    metaDescription: 'Cập nhật tin chuyển nhượng mới nhất',
    articleCount: 15,
    publishedArticleCount: 14
  }
];

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');

    console.log('📂 GET /api/news/categories', { page, limit });

    // Try backend API first
    const backendUrl = process.env.API_BASE_URL || 'http://localhost:3000';
    const backendEndpoint = `${backendUrl}/news/categories`;

    try {
      console.log('🔌 Trying backend API:', backendEndpoint);
      
      const backendResponse = await fetch(backendEndpoint, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        // Add timeout
        signal: AbortSignal.timeout(5000)
      });

      console.log('📡 Response status:', backendResponse.status, 'for', backendEndpoint);

      if (backendResponse.ok) {
        const backendData = await backendResponse.json();
        console.log('✅ Backend API successful:', backendEndpoint);
        
        // Transform backend data to match our interface
        const transformedData = {
          data: backendData.data || backendData.categories || backendData,
          meta: backendData.meta || {
            totalItems: (backendData.data || backendData.categories || backendData).length,
            totalPages: 1,
            currentPage: page,
            limit: limit
          },
          status: 200
        };

        return NextResponse.json(transformedData);
      } else {
        console.log('⚠️ Backend API returned non-OK status:', backendResponse.status);
      }
    } catch (backendError) {
      console.log('⚠️ Backend API failed:', backendError);
    }

    // Fallback to mock data
    console.log('📰 Using mock categories data for development');
    
    // Apply pagination to mock data
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedCategories = MOCK_CATEGORIES.slice(startIndex, endIndex);

    const response = {
      data: paginatedCategories,
      meta: {
        totalItems: MOCK_CATEGORIES.length,
        totalPages: Math.ceil(MOCK_CATEGORIES.length / limit),
        currentPage: page,
        limit: limit
      },
      status: 200
    };

    console.log('✅ Returning mock categories:', paginatedCategories.length, 'categories');

    return NextResponse.json(response);

  } catch (error) {
    console.error('❌ Error in /api/news/categories:', error);

    return NextResponse.json(
      {
        error: 'Internal Server Error',
        message: 'Failed to fetch news categories',
        data: [],
        meta: {
          totalItems: 0,
          totalPages: 0,
          currentPage: 1,
          limit: 20
        },
        status: 500
      },
      { status: 500 }
    );
  }
}
