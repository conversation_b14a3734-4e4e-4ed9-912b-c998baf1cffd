import { NextRequest, NextResponse } from 'next/server';
import {
  createErrorResponse,
  tryMultipleEndpoints,
  getBaseUrl,
  createSuccessResponse,
  parseQueryParams,
  buildQueryString,
  logAPIRequest
} from '@/features/news/utils/apiUtils';

// News by Category API Proxy
export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const { slug } = await params;
    const { searchParams } = new URL(request.url);
    const { page, limit, search } = parseQueryParams(searchParams);

    logAPIRequest('GET', `/api/news/category/${slug}`, { page, limit, search });

    // Build query string for backend
    const queryString = buildQueryString({
      page,
      limit,
      ...(search && { search })
    });

    // Try backend endpoint for category news
    const baseUrl = getBaseUrl();
    const endpoints = [
      `${baseUrl}/news/category/${slug}?${queryString}`,
      `${baseUrl}/public/news/category/${slug}?${queryString}`,
    ];

    try {
      const response = await tryMultipleEndpoints(endpoints);
      const data = await response.json();

      if (data.data && data.data.length > 0) {
        console.log(`✅ Backend category API successful for ${slug}, articles count:`, data.data.length);
        return createSuccessResponse(data, 60);
      }
    } catch (endpointError) {
      console.log(`⚠️ Category endpoints failed for ${slug}`);
      return createErrorResponse('Category news service unavailable', 503);
    }

    // If no data found, return empty result
    console.log(`⚠️ No news data available for category ${slug}`);
    return createSuccessResponse({
      data: [],
      meta: {
        currentPage: page,
        totalPages: 0,
        totalItems: 0,
        hasNextPage: false,
        hasPreviousPage: false
      }
    }, 30);

  } catch (error) {
    console.error('❌ Category news proxy error:', error);
    return createErrorResponse(error);
  }
}
