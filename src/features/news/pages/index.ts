// News Pages Exports
// This file manages all page exports for the news feature

import { newsFeatureConfig } from '../config';

// Dynamic loaders for pages
export const getNewsPage = () => {
        const version = newsFeatureConfig.pages.newsPage;
        return import(`./news-page/v${version}`).then(m => m.default);
};

export const getNewsDetail = () => {
        const version = newsFeatureConfig.pages.newsDetail;
        return import(`./news-detail-page/v${version}`).then(m => m.default);
};

export const getNewsCategoryPage = () => {
        const version = newsFeatureConfig.pages.newsCategoryPage;
        return import(`./news-category-page/v${version}`).then(m => m.default);
};

// Static imports for current versions
export { default as NewsPage } from './news-page';
export { default as NewsDetailPage } from './news-detail-page';
export { default as NewsCategoryPage } from './news-category-page';

// Legacy exports for compatibility
export { default as NewsListPage } from './news-page';
