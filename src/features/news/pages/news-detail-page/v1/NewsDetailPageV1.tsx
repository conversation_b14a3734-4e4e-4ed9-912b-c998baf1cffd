'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { formatDistanceToNow } from 'date-fns';
import { fetchNewsCategories, type NewsCategory } from '@/lib/api/newsCategories';
import { NewsService } from '../../../services/NewsService';
import { SocialShare } from '../../news-page/v1/components/SocialShare';
import { CategorySidebar } from '../../news-page/v1/components/CategorySidebar';
import { SearchBar } from '../../news-page/v1/components/SearchBar';
import { AdvancedFilters } from '../../news-page/v1/components/AdvancedFilters';
import { useReadingAnalytics } from '../../news-page/v1/hooks/useReadingAnalytics';
import { getImageUrl } from '@/features/news/utils/imageUtils';
import './styles/article-content.css';

interface Article {
  id: number;
  title: string;
  slug: string;
  excerpt: string;
  content: string;
  featuredImage: string | null;
  publishedAt: string;
  updatedAt: string;
  tags: string[] | null;
  status: 'published' | 'draft' | 'archived';
  metaTitle: string | null;
  metaDescription: string | null;
  relatedLeagueId: number | null;
  relatedTeamId: number | null;
  relatedPlayerId: number | null;
  relatedFixtureId: number | null;
  viewCount: number;
  shareCount: number;
  likeCount: number;
  isFeatured: boolean;
  priority: number;
  categoryId: number;
  authorId: number;
  author?: {
    id: number;
    username: string;
    fullName: string;
    role: string;
  };
  category: {
    id: number;
    slug: string;
    name: string;
    description: string;
    icon: string;
    color: string;
    sortOrder: number;
    isActive: boolean;
    isPublic: boolean;
    metaTitle: string | null;
    metaDescription: string | null;
    articleCount: number;
    publishedArticleCount: number;
  };
  relatedLeague?: {
    id: number;
    externalId: number;
    name: string;
    country: string;
    logo: string;
    season: number;
  };
  relatedTeam?: {
    id: number;
    externalId: number;
    name: string;
    code: string;
    country: string;
    logo: string;
  };
  relatedPlayer?: {
    id: number;
    externalId: number;
    name: string;
    position: string;
    team: string;
    photo: string;
  };
  relatedFixture?: {
    id: number;
    externalId: number;
    homeTeam: string;
    awayTeam: string;
    date: string;
    status: string;
  };
}

interface RelatedArticle {
  id: number;
  title: string;
  slug: string;
  excerpt: string;
  featuredImage: string | null;
  category: {
    id: number;
    name: string;
    slug: string;
  };
  readTime: number;
  publishedAt: string;
}

interface NewsDetailPageV1Props {
  article: Article;
}

/**
 * News Detail Page V1 - SEO Optimized Article Display
 * 
 * Features:
 * - Structured data for SEO
 * - Reading analytics tracking
 * - Social sharing integration
 * - Responsive design
 * - Accessibility optimized
 */


export default function NewsDetailPageV1({ article }: NewsDetailPageV1Props) {
  const [relatedArticles, setRelatedArticles] = useState<RelatedArticle[]>([]);
  const [isLoadingRelated, setIsLoadingRelated] = useState(false);
  const [categories, setCategories] = useState<NewsCategory[]>([]);
  const [isLoadingCategories, setIsLoadingCategories] = useState(false);
  const [timeAgo, setTimeAgo] = useState<string>('');
  const [isClient, setIsClient] = useState(false);

  // Memoized service instance
  const newsService = React.useMemo(() => NewsService.getInstance(), []);

  // Search and filter states (for sidebar consistency)
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  // Handlers for sidebar components
  const handleCategoryChange = (category: string) => {
    if (category === 'all') {
      window.location.href = '/news';
    } else {
      window.location.href = `/news?category=${category}`;
    }
  };

  const handleSearchChange = (query: string) => {
    setSearchQuery(query);
  };

  const handleSearchSubmit = (query: string) => {
    if (query.trim()) {
      window.location.href = `/news?search=${encodeURIComponent(query.trim())}`;
    } else {
      window.location.href = '/news';
    }
  };

  const handleAdvancedFiltersChange = (filters: any) => {
    // Build query params from filters
    const params = new URLSearchParams();

    if (filters.dateRange) {
      params.set('dateRange', filters.dateRange);
    }
    if (filters.sortBy) {
      params.set('sortBy', filters.sortBy);
    }
    if (filters.featuredOnly) {
      params.set('featured', 'true');
    }

    const queryString = params.toString();
    const url = queryString ? `/news?${queryString}` : '/news';
    window.location.href = url;
  };

  // Analytics tracking
  useReadingAnalytics({
    articleId: article.id.toString(),
    title: article.title,
    category: article.category.name
  });

  // Set client-side flag and time to avoid hydration mismatch
  useEffect(() => {
    setIsClient(true);
    setTimeAgo(formatDistanceToNow(new Date(article.publishedAt), { addSuffix: true }));
    // Set selected category to current article's category
    setSelectedCategory(article.category.slug);
  }, [article.publishedAt, article.category.slug]);

  // Load categories from service with caching
  useEffect(() => {
    const loadCategories = async () => {
      setIsLoadingCategories(true);
      try {
        console.log('📂 Loading news categories via service');
        const response = await newsService.getCategories();
        setCategories(response);
        console.log('✅ Categories loaded via service:', response.length);
      } catch (error) {
        console.error('❌ Failed to load categories:', error);
        // Fallback to empty array - CategorySidebar will use defaults
        setCategories([]);
      } finally {
        setIsLoadingCategories(false);
      }
    };

    loadCategories();
  }, [newsService]);

  // Fetch related articles via service with caching
  useEffect(() => {
    const fetchRelatedArticles = async () => {
      setIsLoadingRelated(true);
      try {
        console.log('🔄 Fetching related articles via service for:', article.slug);
        const articles = await newsService.getRelatedArticles(article.slug, 4);

        // Convert to RelatedArticle format
        const relatedArticles: RelatedArticle[] = articles.map(article => ({
          id: article.id,
          title: article.title,
          slug: article.slug,
          excerpt: article.excerpt,
          featuredImage: article.featuredImage,
          category: {
            id: article.category.id,
            name: article.category.name,
            slug: article.category.slug
          },
          readTime: 5, // Default read time
          publishedAt: article.publishedAt
        }));

        setRelatedArticles(relatedArticles);
        console.log('✅ Related articles loaded via service:', relatedArticles.length);
      } catch (error) {
        console.error('❌ Error fetching related articles:', error);
        setRelatedArticles([]);
      } finally {
        setIsLoadingRelated(false);
      }
    };

    fetchRelatedArticles();
  }, [article.slug, newsService]);

  // Use shared utility for image handling
  const getArticleImageUrl = (featuredImage: string | null, isLarge = false) => {
    const fallbackText = isLarge ? 'News+Article' : 'News+Image';
    return getImageUrl(featuredImage, fallbackText);
  };

  // Helper function for sports-related images (logos, photos)
  // Combines API response paths with NEXT_PUBLIC_CDN_DOMAIN_PICTURE for direct URLs
  // Example:
  // - API Response: "public/images/leagues/71.png"
  // - CDN Domain: "http://172.31.213.61"
  // - Final URL: "http://172.31.213.61/public/images/leagues/71.png"
  const getSportsImageUrl = (imagePath: string | null | undefined, fallbackText: string = 'IMG') => {
    if (!imagePath) {
      return `https://via.placeholder.com/48x48/f3f4f6/9ca3af?text=${fallbackText}`;
    }

    // If it's already a full URL (starts with http), use it directly
    if (imagePath.startsWith('http')) {
      return imagePath;
    }

    // Combine with CDN domain, ensuring proper URL formatting
    const cdnDomain = process.env.NEXT_PUBLIC_CDN_DOMAIN_PICTURE;
    if (!cdnDomain) {
      return `https://via.placeholder.com/48x48/f3f4f6/9ca3af?text=${fallbackText}`;
    }

    // Ensure proper URL formatting - add slash if needed
    const cleanPath = imagePath.startsWith('/') ? imagePath : `/${imagePath}`;
    return `${cdnDomain}${cleanPath}`;
  };

  const imageUrl = getArticleImageUrl(article.featuredImage, true);
  const fallbackImageUrl = 'https://via.placeholder.com/800x450/f3f4f6/9ca3af?text=News+Image';

  // Use consistent URL for both server and client to avoid hydration mismatch
  const articleUrl = `${process.env.NEXT_PUBLIC_FE_DOMAIN || 'http://localhost:5000'}/news/${article.slug}`;

  // Create structured data object to ensure consistency
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "NewsArticle",
    "headline": article.title,
    "description": article.excerpt,
    "image": imageUrl,
    "datePublished": article.publishedAt,
    "dateModified": article.updatedAt,
    "author": {
      "@type": "Person",
      "name": article.author?.fullName || article.author?.username || "Sports News Team"
    },
    "publisher": {
      "@type": "Organization",
      "name": "Sports News Platform",
      "logo": {
        "@type": "ImageObject",
        "url": "/images/logo.png"
      }
    },
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": articleUrl
    },
    "articleSection": article.category.name,
    "keywords": (article.tags || []).join(", "),
    "wordCount": article.content.length,
    "timeRequired": "PT5M",
    "interactionStatistic": [
      {
        "@type": "InteractionCounter",
        "interactionType": "https://schema.org/ReadAction",
        "userInteractionCount": article.viewCount || 0
      },
      {
        "@type": "InteractionCounter",
        "interactionType": "https://schema.org/ShareAction",
        "userInteractionCount": article.shareCount || 0
      },
      {
        "@type": "InteractionCounter",
        "interactionType": "https://schema.org/LikeAction",
        "userInteractionCount": article.likeCount || 0
      }
    ]
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Structured Data for SEO */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData)
        }}
      />

      {/* Page Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <h1 className="text-3xl font-bold text-gray-900">News Detail</h1>
          <p className="mt-2 text-gray-600">Read the latest sports news and updates</p>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8 min-w-0">
          {/* Left Sidebar - Categories */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 sticky top-8">
              <CategorySidebar
                categories={categories}
                selectedCategory={selectedCategory}
                onCategoryChange={handleCategoryChange}
              />

              {/* Desktop Search */}
              <div className="hidden md:block mt-6 pt-6 border-t border-gray-200">
                <SearchBar
                  value={searchQuery}
                  onChange={handleSearchChange}
                  onSubmit={handleSearchSubmit}
                  placeholder="Search news..."
                />
              </div>

              {/* Advanced Filters */}
              <div className="mt-4">
                <AdvancedFilters
                  onFiltersChange={handleAdvancedFiltersChange}
                />
              </div>

              {/* Back to News */}
              <div className="mt-6 pt-6 border-t border-gray-200">
                <Link
                  href="/news"
                  className="inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors font-medium text-sm"
                >
                  ← Back to All News
                </Link>
              </div>
            </div>
          </div>

          {/* Right Content - Article Detail */}
          <div className="lg:col-span-3 min-w-0">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
              {/* Article Content */}
              <div className="p-6 lg:p-8">
                {/* Breadcrumb Navigation */}
                <nav className="mb-6" aria-label="Breadcrumb">
                  <ol className="flex items-center space-x-2 text-sm text-gray-500">
                    <li>
                      <Link href="/" className="hover:text-gray-700 transition-colors">
                        Home
                      </Link>
                    </li>
                    <li>/</li>
                    <li>
                      <Link href="/news" className="hover:text-gray-700 transition-colors">
                        News
                      </Link>
                    </li>
                    <li>/</li>
                    <li>
                      <Link
                        href={`/news?category=${article.category.slug}`}
                        className="hover:text-gray-700 transition-colors"
                      >
                        {article.category.name}
                      </Link>
                    </li>
                    <li>/</li>
                    <li className="text-gray-900 font-medium truncate">
                      {article.title}
                    </li>
                  </ol>
                </nav>

                {/* Article Header */}
                <header className="mb-8">
                  {/* Category Badge */}
                  <div className="mb-4">
                    <Link
                      href={`/news?category=${article.category.slug}`}
                      className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 hover:bg-blue-200 transition-colors"
                    >
                      {article.category.name}
                    </Link>
                  </div>

                  {/* Title */}
                  <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 leading-tight mb-6">
                    {article.title}
                  </h1>

                  {/* Excerpt */}
                  <p className="text-xl text-gray-600 leading-relaxed mb-6">
                    {article.excerpt}
                  </p>

                  {/* Article Meta */}
                  <div className="flex flex-wrap items-center justify-between gap-4 py-4 border-t border-b border-gray-200">
                    <div className="flex items-center space-x-4">
                      <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold text-sm">
                        {article.author?.fullName?.charAt(0) || article.author?.username?.charAt(0) || 'A'}
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">
                          {article.author?.fullName || article.author?.username || 'Sports News Team'}
                        </p>
                        <p className="text-sm text-gray-500">
                          {article.author?.role === 'admin' ? 'Administrator' : 'Sports Journalist'}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-6 text-sm text-gray-500">
                      <time dateTime={article.publishedAt}>
                        {timeAgo || new Date(article.publishedAt).toLocaleDateString()}
                      </time>
                      {article.isFeatured && (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                          Featured
                        </span>
                      )}
                      <span>{(article.viewCount || 0).toLocaleString()} views</span>
                    </div>
                  </div>

                  {/* Article Info & Tags */}
                  <div className="flex items-center justify-between mt-4">
                    <div className="flex items-center space-x-4">
                      {/* Category Badge */}
                      <span
                        className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium"
                        style={{
                          backgroundColor: `${article.category.color}20`,
                          color: article.category.color
                        }}
                      >
                        {article.category.name}
                      </span>

                      {/* Tags */}
                      {article.tags && article.tags.length > 0 && (
                        <>
                          {article.tags.slice(0, 3).map((tag) => (
                            <Link
                              key={tag}
                              href={`/news?search=${tag}`}
                              className="text-sm text-blue-600 hover:text-blue-800 hover:underline"
                            >
                              #{tag}
                            </Link>
                          ))}
                          {article.tags.length > 3 && (
                            <span className="text-sm text-gray-500">
                              +{article.tags.length - 3} more
                            </span>
                          )}
                        </>
                      )}
                    </div>

                    <SocialShare
                      url={articleUrl}
                      title={article.title}
                      description={article.excerpt}
                      variant="horizontal"
                    />
                  </div>
                </header>

                {/* Featured Image */}
                <div className="mb-8">
                  <div className="relative aspect-video overflow-hidden rounded-lg bg-gray-200">
                    <img
                      src={imageUrl}
                      alt={article.title}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = fallbackImageUrl;
                      }}
                    />
                  </div>
                </div>

                {/* Article Content */}
                <article className="prose prose-lg max-w-none overflow-hidden">
                  <div className="text-gray-900 leading-relaxed">
                    {/* Render HTML content from API */}
                    <div
                      className="article-content overflow-hidden"
                      dangerouslySetInnerHTML={{ __html: article.content }}
                    />
                  </div>
                </article>

                {/* Related Sports Data */}
                {(article.relatedLeague || article.relatedTeam || article.relatedPlayer || article.relatedFixture) && (
                  <div className="mt-12 p-6 bg-gray-50 rounded-lg">
                    <h3 className="text-lg font-semibold text-gray-900 mb-6">Related Sports Information</h3>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {/* Related League */}
                      {article.relatedLeague && (
                        <div className="flex items-center space-x-4 p-4 bg-white rounded-lg border">
                          <div className="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center overflow-hidden">
                            <img
                              src={getSportsImageUrl(article.relatedLeague.logo, 'L')}
                              alt={article.relatedLeague.name}
                              className="w-full h-full object-cover"
                              onError={(e) => {
                                const target = e.target as HTMLImageElement;
                                target.src = 'https://via.placeholder.com/48x48/f3f4f6/9ca3af?text=L';
                              }}
                            />
                          </div>
                          <div className="flex-1">
                            <p className="text-sm font-medium text-gray-900">{article.relatedLeague.name}</p>
                            <p className="text-xs text-gray-500">{article.relatedLeague.country} • Season {article.relatedLeague.season}</p>
                          </div>
                          <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">League</span>
                        </div>
                      )}

                      {/* Related Team */}
                      {article.relatedTeam && (
                        <div className="flex items-center space-x-4 p-4 bg-white rounded-lg border">
                          <div className="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center overflow-hidden">
                            <img
                              src={getSportsImageUrl(article.relatedTeam.logo, 'T')}
                              alt={article.relatedTeam.name}
                              className="w-full h-full object-cover"
                              onError={(e) => {
                                const target = e.target as HTMLImageElement;
                                target.src = 'https://via.placeholder.com/48x48/f3f4f6/9ca3af?text=T';
                              }}
                            />
                          </div>
                          <div className="flex-1">
                            <p className="text-sm font-medium text-gray-900">{article.relatedTeam.name}</p>
                            <p className="text-xs text-gray-500">{article.relatedTeam.country} • {article.relatedTeam.code}</p>
                          </div>
                          <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Team</span>
                        </div>
                      )}

                      {/* Related Player */}
                      {article.relatedPlayer && (
                        <div className="flex items-center space-x-4 p-4 bg-white rounded-lg border">
                          <div className="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center overflow-hidden">
                            <img
                              src={getSportsImageUrl(article.relatedPlayer?.photo, 'P')}
                              alt={article.relatedPlayer.name}
                              className="w-full h-full object-cover"
                              onError={(e) => {
                                const target = e.target as HTMLImageElement;
                                target.src = 'https://via.placeholder.com/48x48/f3f4f6/9ca3af?text=P';
                              }}
                            />
                          </div>
                          <div className="flex-1">
                            <p className="text-sm font-medium text-gray-900">{article.relatedPlayer.name}</p>
                            <p className="text-xs text-gray-500">{article.relatedPlayer.position} • {article.relatedPlayer.team}</p>
                          </div>
                          <span className="text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded">Player</span>
                        </div>
                      )}

                      {/* Related Fixture */}
                      {article.relatedFixture && (
                        <div className="flex items-center space-x-4 p-4 bg-white rounded-lg border">
                          <div className="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
                            <svg className="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                          </div>
                          <div className="flex-1">
                            <p className="text-sm font-medium text-gray-900">
                              {article.relatedFixture.homeTeam} vs {article.relatedFixture.awayTeam}
                            </p>
                            <p className="text-xs text-gray-500">
                              {new Date(article.relatedFixture.date).toLocaleDateString()} • {article.relatedFixture.status}
                            </p>
                          </div>
                          <span className="text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded">Match</span>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Article Footer */}
                <footer className="mt-12 pt-8 border-t border-gray-200">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                    <div className="flex flex-col sm:flex-row sm:items-center gap-4">
                      <span className="text-sm text-gray-500">Share this article:</span>
                      <SocialShare
                        url={articleUrl}
                        title={article.title}
                        description={article.excerpt}
                        variant="horizontal"
                      />
                    </div>

                    <div className="flex items-center space-x-6 text-sm text-gray-500">
                      <span>Published {timeAgo || new Date(article.publishedAt).toLocaleDateString()}</span>
                      <span>•</span>
                      <span>{(article.viewCount || 0).toLocaleString()} views</span>
                      {article.priority > 5 && (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                          High Priority
                        </span>
                      )}
                    </div>
                  </div>

                  {/* Author Info */}
                  <div className="mt-8 p-6 bg-gray-50 rounded-lg">
                    <div className="flex items-start space-x-4">
                      <div className="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold text-xl">
                        {article.author?.fullName?.charAt(0) || article.author?.username?.charAt(0) || 'A'}
                      </div>
                      <div className="flex-1">
                        <h4 className="font-semibold text-gray-900">
                          {article.author?.fullName || article.author?.username || 'Sports News Team'}
                        </h4>
                        <p className="text-gray-600 text-sm mt-1">
                          {article.author?.role === 'admin'
                            ? 'System Administrator - Managing sports news and content'
                            : 'Professional sports journalist covering the latest news and updates'
                          }
                        </p>
                        <div className="flex items-center space-x-4 mt-3 text-sm text-gray-500">
                          <span>Published {timeAgo || new Date(article.publishedAt).toLocaleDateString()}</span>
                          <span>•</span>
                          <span>Article ID: {article.id}</span>
                          {article.status === 'published' && (
                            <>
                              <span>•</span>
                              <span className="text-green-600">Published</span>
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Related Articles */}
                  <div className="mt-12">
                    <h3 className="text-2xl font-bold text-gray-900 mb-6">Related Articles</h3>

                    {isLoadingRelated ? (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {[1, 2].map((index) => (
                          <div key={index} className="bg-gray-50 rounded-lg overflow-hidden animate-pulse">
                            <div className="aspect-video bg-gray-200"></div>
                            <div className="p-4">
                              <div className="h-4 bg-gray-200 rounded mb-2"></div>
                              <div className="h-6 bg-gray-200 rounded mb-2"></div>
                              <div className="h-4 bg-gray-200 rounded"></div>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : relatedArticles.length > 0 ? (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {relatedArticles.slice(0, 4).map((relatedArticle) => {
                          const relatedImageUrl = getImageUrl(relatedArticle.featuredImage, false);

                          return (
                            <Link
                              key={relatedArticle.id}
                              href={`/news/${relatedArticle.slug}`}
                              className="group block bg-gray-50 rounded-lg overflow-hidden hover:bg-white hover:shadow-md transition-all duration-200"
                            >
                              <div className="aspect-video bg-gray-200">
                                <img
                                  src={relatedImageUrl}
                                  alt={relatedArticle.title}
                                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                                  onError={(e) => {
                                    const target = e.target as HTMLImageElement;
                                    target.src = 'https://via.placeholder.com/400x225/f3f4f6/9ca3af?text=News+Image';
                                  }}
                                />
                              </div>
                              <div className="p-4">
                                <div className="flex items-center space-x-2 mb-2">
                                  <span className="text-xs font-medium text-blue-600 bg-blue-100 px-2 py-1 rounded">
                                    {relatedArticle.category.name}
                                  </span>
                                  <span className="text-xs text-gray-500">
                                    {new Date(relatedArticle.publishedAt).toLocaleDateString()}
                                  </span>
                                </div>
                                <h4 className="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors line-clamp-2">
                                  {relatedArticle.title}
                                </h4>
                                <p className="text-sm text-gray-600 mt-2 line-clamp-2">
                                  {relatedArticle.excerpt}
                                </p>
                              </div>
                            </Link>
                          );
                        })}
                      </div>
                    ) : (
                      <div className="text-center py-8">
                        <p className="text-gray-500">No related articles found.</p>
                      </div>
                    )}
                  </div>

                </footer>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
