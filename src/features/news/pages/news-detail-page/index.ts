// News Detail Page - Version Management System
// This file manages different versions of the news detail page component

import { newsFeatureConfig } from '../../config';

// Dynamic version loading
export const getNewsDetailPage = () => {
        const version = newsFeatureConfig.pages.newsDetail;
        return import(`./v${version}`).then(m => m.default);
};

// Static import for current version (V2)
export { default } from './v2';
export { default as NewsDetailPage } from './v2';
