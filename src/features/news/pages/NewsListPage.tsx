// News List Page Component
'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import {
  NewsGrid,
  NewsHero,
  CategoryFilter,
  SearchBar,
  Pagination
} from '../components';
import {
  useNews,
  useNewsCategories,
  useNewsFilters
} from '../hooks';
import { NewsFilter, NewsSortOption } from '../types';

interface NewsListPageProps {
  initialCategory?: string;
  showHero?: boolean;
  variant?: 'default' | 'category' | 'search';
  className?: string;
}

/**
 * News List Page - Main news listing page with filtering and pagination
 * @version 1.0.0
 * @since 2024-01-25
 */
export default function NewsListPage({
  initialCategory,
  showHero = true,
  variant = 'default',
  className = ''
}: NewsListPageProps) {
  const router = useRouter();
  const searchParams = useSearchParams();

  // Initialize filters from URL params
  const initialFilters: NewsFilter = {
    category: initialCategory || searchParams.get('category') || undefined,
    search: searchParams.get('search') || undefined,
    author: searchParams.get('author') || undefined,
    tags: searchParams.get('tags')?.split(',') || undefined
  };

  const initialSort = (searchParams.get('sort') as NewsSortOption) || 'publishedAt_desc';
  const initialPage = parseInt(searchParams.get('page') || '1', 10);

  // Hooks
  const {
    filters,
    sort,
    updateFilter,
    updateSort,
    clearFilters,
    hasActiveFilters,
    filterCount,
    getQueryParams
  } = useNewsFilters(initialFilters);

  const {
    articles,
    loading,
    error,
    hasMore,
    loadMore,
    refresh,
    currentPage
  } = useNews({
    initialFilters,
    initialSort,
    initialPage,
    autoFetch: true
  });

  const {
    categories,
    loading: categoriesLoading,
    selectedCategory,
    selectCategory
  } = useNewsCategories();

  // Debug categories
  console.log('🎯 NewsListPage categories state:', categories?.length || 0, 'categories');
  console.log('🎯 NewsListPage categoriesLoading:', categoriesLoading);

  // Test direct API call
  useEffect(() => {
    const testFetchCategories = async () => {
      try {
        console.log('🧪 Testing direct API call...');
        const response = await fetch('/api/news/categories');
        const data = await response.json();
        console.log('🧪 Direct API response:', data.data?.length || 0, 'categories');
      } catch (error) {
        console.error('🧪 Direct API error:', error);
      }
    };

    testFetchCategories();
  }, []);

  const [searchQuery, setSearchQuery] = useState(filters.search || '');
  const [featuredArticle, setFeaturedArticle] = useState(null);

  // Update URL when filters change
  useEffect(() => {
    const params = new URLSearchParams();
    const queryParams = getQueryParams();

    Object.entries(queryParams).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.set(key, String(value));
      }
    });

    if (currentPage > 1) {
      params.set('page', String(currentPage));
    }

    const newUrl = `${window.location.pathname}${params.toString() ? `?${params.toString()}` : ''}`;
    router.replace(newUrl, { scroll: false });
  }, [filters, sort, currentPage, getQueryParams, router]);

  // Handle search
  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query);
    updateFilter('search', query || undefined);
  }, [updateFilter]);

  // Handle category selection
  const handleCategorySelect = useCallback((categorySlug: string | undefined) => {
    selectCategory(categorySlug);
    updateFilter('category', categorySlug);

    // Redirect to appropriate URL
    if (categorySlug) {
      router.push(`/news-category/${categorySlug}?page=1`);
    } else {
      router.push('/news?page=1');
    }
  }, [selectCategory, updateFilter, router]);

  // Handle sort change
  const handleSortChange = useCallback((newSort: NewsSortOption) => {
    updateSort(newSort);
  }, [updateSort]);

  // Handle pagination
  const handlePageChange = useCallback((page: number) => {
    // This will be handled by the useNews hook
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }, []);

  // Clear all filters
  const handleClearFilters = useCallback(() => {
    setSearchQuery('');
    selectCategory(undefined);
    clearFilters();
  }, [clearFilters, selectCategory]);

  // Get featured article for hero
  useEffect(() => {
    if (showHero && articles.length > 0 && !filters.search && !filters.category) {
      setFeaturedArticle(articles[0]);
    } else {
      setFeaturedArticle(null);
    }
  }, [articles, showHero, filters.search, filters.category]);

  // Handle article click
  const handleArticleClick = useCallback((article: any) => {
    router.push(`/news/${article.slug}`);
  }, [router]);

  return (
    <div className={`min-h-screen bg-gray-50 ${className}`}>
      {/* Hero Section */}
      {showHero && variant === 'default' && (
        <NewsHero
          featuredArticle={featuredArticle}
          recentArticles={articles.slice(1, 4)}
          loading={loading && articles.length === 0}
          error={error}
          variant="default"
          showSearch={true}
          searchProps={{
            value: searchQuery,
            onChange: setSearchQuery,
            onSubmit: handleSearch,
            placeholder: 'Tìm kiếm tin tức thể thao...'
          }}
          onArticleClick={handleArticleClick}
          className="mb-8"
        />
      )}

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        {/* Page Header for non-default variants */}
        {variant !== 'default' && (
          <div className="mb-8">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">
                  {variant === 'category' && selectedCategory
                    ? `Tin tức ${categories?.find(c => c?.slug === selectedCategory)?.name || selectedCategory}`
                    : variant === 'search' && filters.search
                      ? `Kết quả tìm kiếm: "${filters.search}"`
                      : 'Tin tức thể thao'
                  }
                </h1>
                {articles.length > 0 && (
                  <p className="text-gray-600 mt-2">
                    Tìm thấy {articles.length} bài viết
                    {hasActiveFilters && (
                      <span className="ml-2">
                        ({filterCount} bộ lọc đang áp dụng)
                      </span>
                    )}
                  </p>
                )}
              </div>

              {/* Search Bar for category/search variants */}
              <div className="w-full md:w-96">
                <SearchBar
                  value={searchQuery}
                  onChange={setSearchQuery}
                  onSubmit={handleSearch}
                  placeholder="Tìm kiếm tin tức..."
                  variant="compact"
                />
              </div>
            </div>
          </div>
        )}

        {/* Filters Section */}
        <div className="mb-8">
          <div className="flex flex-col lg:flex-row gap-6">
            {/* Category Filter */}
            <div className="lg:w-64 flex-shrink-0">
              <CategoryFilter
                categories={categories}
                selectedCategory={selectedCategory}
                onCategoryChange={handleCategorySelect}
                variant="sidebar"
              />
            </div>

            {/* Main Content Area */}
            <div className="flex-1">
              {/* Filter Controls */}
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
                <div className="flex items-center gap-4">
                  {/* Sort Dropdown */}
                  <select
                    value={sort}
                    onChange={(e) => handleSortChange(e.target.value as NewsSortOption)}
                    className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="publishedAt_desc">Mới nhất</option>
                    <option value="publishedAt_asc">Cũ nhất</option>
                    <option value="viewCount_desc">Xem nhiều nhất</option>
                    <option value="shareCount_desc">Chia sẻ nhiều nhất</option>
                    <option value="title_asc">Tiêu đề A-Z</option>
                    <option value="title_desc">Tiêu đề Z-A</option>
                  </select>

                  {/* Clear Filters */}
                  {hasActiveFilters && (
                    <button
                      onClick={handleClearFilters}
                      className="px-3 py-2 text-sm text-gray-600 hover:text-gray-900 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors duration-200"
                    >
                      Xóa bộ lọc ({filterCount})
                    </button>
                  )}
                </div>

                {/* View Toggle (Future enhancement) */}
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-600">Hiển thị:</span>
                  <div className="flex border border-gray-300 rounded-md overflow-hidden">
                    <button className="px-3 py-1 text-sm bg-blue-600 text-white">
                      Lưới
                    </button>
                    <button className="px-3 py-1 text-sm bg-white text-gray-600 hover:bg-gray-50">
                      Danh sách
                    </button>
                  </div>
                </div>
              </div>

              {/* News Grid */}
              <NewsGrid
                articles={articles}
                loading={loading}
                error={error}
                variant="default"
                columns={3}
                gap="md"
                showLoadMore={hasMore}
                hasMore={hasMore}
                onLoadMore={loadMore}
                onArticleClick={handleArticleClick}
                cardVariant="default"
                showCategory={!filters.category}
                showAuthor={true}
                showDate={true}
                showExcerpt={true}
                showReadingTime={true}
                showImage={true}
              />

              {/* Pagination */}
              {articles.length > 0 && (
                <div className="mt-8">
                  <Pagination
                    currentPage={currentPage}
                    totalPages={Math.ceil(articles.length / 20)} // This should come from API
                    onPageChange={handlePageChange}
                    showFirstLast={true}
                    showPrevNext={true}
                    maxVisiblePages={5}
                    variant="default"
                    size="md"
                    showInfo={true}
                    totalItems={articles.length}
                    itemsPerPage={20}
                  />
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
