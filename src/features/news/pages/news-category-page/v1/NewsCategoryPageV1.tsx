'use client';

import React, { useEffect, useState } from 'react';
import { useParams, useSearchParams } from 'next/navigation';
import { CategorySidebar, NewsList, Pagination } from '../../../components';

// Types
interface NewsArticle {
  id: number;
  title: string;
  slug: string;
  excerpt: string;
  featuredImage?: string;
  publishedAt: string;
  authorId: number;
  category: {
    id: number;
    name: string;
    slug: string;
    color?: string;
  };
  viewCount: number;
  isFeatured: boolean;
}

interface NewsAPIResponse {
  data: NewsArticle[];
  meta: {
    totalItems: number;
    totalPages: number;
    currentPage: number;
    limit: number;
  };
  status: number;
}

interface Category {
  id: number;
  name: string;
  slug: string;
  description?: string;
  color?: string;
}

export default function NewsCategoryPageV1() {
  const params = useParams();
  const searchParams = useSearchParams();
  const categorySlug = params.slug as string;
  const currentPage = parseInt(searchParams.get('page') || '1');

  const [articles, setArticles] = useState<NewsArticle[]>([]);
  const [category, setCategory] = useState<Category | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [meta, setMeta] = useState({
    totalItems: 0,
    totalPages: 0,
    currentPage: 1,
    limit: 20
  });

  // Fetch category info
  useEffect(() => {
    const fetchCategory = async () => {
      try {
        const response = await fetch('/api/news/categories');
        const data = await response.json();
        
        if (data.status === 200 && data.data) {
          const foundCategory = data.data.find((cat: Category) => cat.slug === categorySlug);
          setCategory(foundCategory || null);
        }
      } catch (err) {
        console.error('Error fetching category:', err);
      }
    };

    if (categorySlug) {
      fetchCategory();
    }
  }, [categorySlug]);

  // Fetch news articles by category
  useEffect(() => {
    const fetchNews = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const response = await fetch(`/api/news/category/${categorySlug}?page=${currentPage}&limit=20`);
        const data: NewsAPIResponse = await response.json();
        
        if (data.status === 200 && data.data) {
          setArticles(data.data);
          setMeta(data.meta);
        } else {
          setError('Không thể tải tin tức');
        }
      } catch (err) {
        setError('Lỗi kết nối API');
        console.error('Error fetching news:', err);
      } finally {
        setLoading(false);
      }
    };

    if (categorySlug) {
      fetchNews();
    }
  }, [categorySlug, currentPage]);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                {category ? category.name : 'Tin tức theo chuyên mục'}
              </h1>
              <p className="text-gray-600 mt-2">
                {category?.description || `Tin tức chuyên mục ${categorySlug}`}
              </p>
            </div>
            <div className="text-sm text-gray-500">
              {!loading && meta.totalItems > 0 && (
                <span>Tổng cộng {meta.totalItems} bài viết</span>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Left Sidebar - Categories */}
          <aside className="lg:w-1/4">
            <CategorySidebar />
          </aside>

          {/* Right Content - News List */}
          <main className="lg:w-3/4">
            {error ? (
              <div className="bg-white rounded-lg shadow-sm border p-8 text-center">
                <div className="text-red-500">
                  <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Có lỗi xảy ra</h3>
                  <p className="text-gray-500">{error}</p>
                </div>
              </div>
            ) : (
              <>
                {/* News List */}
                <NewsList articles={articles} loading={loading} />

                {/* Pagination */}
                {!loading && meta.totalPages > 1 && (
                  <div className="mt-8">
                    <Pagination
                      currentPage={meta.currentPage}
                      totalPages={meta.totalPages}
                      baseUrl={`/news-category/${categorySlug}`}
                    />
                  </div>
                )}
              </>
            )}
          </main>
        </div>
      </div>
    </div>
  );
}
