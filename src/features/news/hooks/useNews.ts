// useNews Hook
// Custom hook for managing news articles data

'use client';

import { useState, useEffect, useCallback } from 'react';
import { newsService } from '../services/NewsService';
import { NewsArticle, NewsFilter, NewsSortOption, UseNewsReturn } from '../types';

interface UseNewsOptions {
  initialFilters?: NewsFilter;
  initialSort?: NewsSortOption;
  initialPage?: number;
  autoFetch?: boolean;
}

/**
 * Custom hook for managing news articles
 * @param options - Configuration options
 * @returns News data and management functions
 */
export function useNews(options: UseNewsOptions = {}): UseNewsReturn {
  const {
    initialFilters = {},
    initialSort = 'publishedAt_desc',
    initialPage = 1,
    autoFetch = true
  } = options;
  const [articles, setArticles] = useState<NewsArticle[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(true);
  const [currentPage, setCurrentPage] = useState(initialPage);
  const [filters, setFilters] = useState<NewsFilter>(initialFilters);
  const [sort, setSort] = useState<NewsSortOption>(initialSort);

  /**
   * Fetch articles with current filters and sort
   */
  const fetchArticles = useCallback(async (page: number = 1, append: boolean = false) => {
    try {
      setLoading(true);
      setError(null);

      const response = await newsService.getArticles(filters, sort, page);

      if (append) {
        setArticles(prev => [...prev, ...response.data]);
      } else {
        setArticles(response.data);
      }

      setHasMore(response.meta.hasNextPage);
      setCurrentPage(page);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Có lỗi xảy ra khi tải tin tức');
      console.error('Error fetching articles:', err);
    } finally {
      setLoading(false);
    }
  }, [filters, sort]);

  /**
   * Load more articles (pagination)
   */
  const loadMore = useCallback(() => {
    if (!loading && hasMore) {
      fetchArticles(currentPage + 1, true);
    }
  }, [loading, hasMore, currentPage, fetchArticles]);

  /**
   * Refresh articles (reload from first page)
   */
  const refresh = useCallback(() => {
    setCurrentPage(1);
    fetchArticles(1, false);
  }, [fetchArticles]);

  /**
   * Update filters and refetch
   */
  const updateFilters = useCallback((newFilters: NewsFilter) => {
    setFilters(newFilters);
    setCurrentPage(1);
  }, []);

  /**
   * Update sort and refetch
   */
  const updateSort = useCallback((newSort: NewsSortOption) => {
    setSort(newSort);
    setCurrentPage(1);
  }, []);

  // Initial fetch
  useEffect(() => {
    if (autoFetch) {
      fetchArticles(1, false);
    }
  }, [fetchArticles, autoFetch]);

  // Refetch when filters or sort change
  useEffect(() => {
    if (currentPage === 1) {
      fetchArticles(1, false);
    }
  }, [filters, sort, currentPage, fetchArticles]);

  return {
    articles,
    loading,
    error,
    hasMore,
    loadMore,
    refresh,
    updateFilters,
    updateSort,
    currentPage,
    filters,
    sort
  };
}
