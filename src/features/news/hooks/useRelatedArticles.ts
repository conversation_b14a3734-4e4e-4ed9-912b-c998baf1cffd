// useRelatedArticles Hook
// Custom hook for managing related articles

'use client';

import { useState, useEffect, useCallback } from 'react';
import { NewsService } from '../services/NewsService';
import { NewsArticle } from '../types';

interface UseRelatedArticlesReturn {
  relatedArticles: NewsArticle[];
  loading: boolean;
  error: string | null;
  fetchRelatedArticles: (slug: string, limit?: number) => Promise<void>;
  refresh: () => Promise<void>;
}

/**
 * Custom hook for managing related articles
 * @param articleSlug - Current article slug
 * @param limit - Number of related articles to fetch
 * @param autoFetch - Whether to automatically fetch on mount
 * @returns Related articles data and management functions
 */
export function useRelatedArticles(
  articleSlug?: string,
  limit: number = 5,
  autoFetch: boolean = true
): UseRelatedArticlesReturn {
  const [relatedArticles, setRelatedArticles] = useState<NewsArticle[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentSlug, setCurrentSlug] = useState<string | undefined>(articleSlug);

  /**
   * Fetch related articles
   */
  const fetchRelatedArticles = useCallback(async (slug: string, articleLimit: number = limit) => {
    try {
      setLoading(true);
      setError(null);
      setCurrentSlug(slug);

      const newsService = NewsService.getInstance();
      const related = await newsService.getRelatedArticles(slug, articleLimit);
      setRelatedArticles(related);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Có lỗi xảy ra khi tải bài viết liên quan');
      console.error('Error fetching related articles:', err);
    } finally {
      setLoading(false);
    }
  }, [limit]);

  /**
   * Refresh related articles
   */
  const refresh = useCallback(async () => {
    if (currentSlug) {
      await fetchRelatedArticles(currentSlug, limit);
    }
  }, [currentSlug, limit, fetchRelatedArticles]);

  /**
   * Filter related articles by category
   */
  const filterByCategory = useCallback((categorySlug: string) => {
    return relatedArticles.filter(article => article.category.slug === categorySlug);
  }, [relatedArticles]);

  /**
   * Filter related articles by tags
   */
  const filterByTags = useCallback((tags: string[]) => {
    return relatedArticles.filter(article =>
      article.tags.some(tag => tags.includes(tag))
    );
  }, [relatedArticles]);

  /**
   * Get most recent related articles
   */
  const getMostRecent = useCallback((count: number = 3) => {
    return relatedArticles
      .sort((a, b) => new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime())
      .slice(0, count);
  }, [relatedArticles]);

  /**
   * Get most popular related articles
   */
  const getMostPopular = useCallback((count: number = 3) => {
    return relatedArticles
      .sort((a, b) => (b.viewCount + b.shareCount) - (a.viewCount + a.shareCount))
      .slice(0, count);
  }, [relatedArticles]);

  /**
   * Get related articles by same author
   */
  const getBySameAuthor = useCallback((authorId: string, count: number = 3) => {
    return relatedArticles
      .filter(article => article.author.id === authorId)
      .slice(0, count);
  }, [relatedArticles]);

  /**
   * Shuffle related articles
   */
  const shuffle = useCallback(() => {
    const shuffled = [...relatedArticles].sort(() => Math.random() - 0.5);
    setRelatedArticles(shuffled);
  }, [relatedArticles]);

  /**
   * Load more related articles
   */
  const loadMore = useCallback(async (additionalLimit: number = 5) => {
    if (!currentSlug) return;

    try {
      setLoading(true);
      const newLimit = relatedArticles.length + additionalLimit;
      const newsService = NewsService.getInstance();
      const related = await newsService.getRelatedArticles(currentSlug, newLimit);
      setRelatedArticles(related);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Có lỗi xảy ra khi tải thêm bài viết');
      console.error('Error loading more related articles:', err);
    } finally {
      setLoading(false);
    }
  }, [currentSlug, relatedArticles.length]);

  /**
   * Check if article is in related list
   */
  const isRelated = useCallback((articleId: string) => {
    return relatedArticles.some(article => article.id === articleId);
  }, [relatedArticles]);

  /**
   * Get related articles statistics
   */
  const getStatistics = useCallback(() => {
    const totalViews = relatedArticles.reduce((sum, article) => sum + article.viewCount, 0);
    const totalShares = relatedArticles.reduce((sum, article) => sum + article.shareCount, 0);
    const categories = [...new Set(relatedArticles.map(article => article.category.name))];
    const authors = [...new Set(relatedArticles.map(article => article.author.name))];

    return {
      totalArticles: relatedArticles.length,
      totalViews,
      totalShares,
      uniqueCategories: categories.length,
      uniqueAuthors: <AUTHORS>
      categories,
      authors
    };
  }, [relatedArticles]);

  // Auto-fetch related articles on mount or slug change
  useEffect(() => {
    if (autoFetch && articleSlug) {
      fetchRelatedArticles(articleSlug, limit);
    }
  }, [autoFetch, articleSlug, limit, fetchRelatedArticles]);

  return {
    relatedArticles,
    loading,
    error,
    fetchRelatedArticles,
    refresh,
    filterByCategory,
    filterByTags,
    getMostRecent,
    getMostPopular,
    getBySameAuthor,
    shuffle,
    loadMore,
    isRelated,
    getStatistics
  };
}
