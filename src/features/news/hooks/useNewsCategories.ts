// useNewsCategories Hook
// Custom hook for managing news categories

'use client';

import { useState, useEffect, useCallback } from 'react';
import { NewsService } from '../services/NewsService';
import { NewsCategory, UseNewsCategoriesReturn } from '../types';

/**
 * Custom hook for managing news categories
 * @param autoFetch - Whether to automatically fetch categories on mount
 * @returns Categories data and management functions
 */
export function useNewsCategories(
  autoFetch: boolean = true
): UseNewsCategoriesReturn {
  console.log('🎯 useNewsCategories hook initialized with autoFetch:', autoFetch);

  const [categories, setCategories] = useState<NewsCategory[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string | undefined>(undefined);

  /**
   * Fetch categories from API
   */
  const fetchCategories = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const newsService = NewsService.getInstance();
      const fetchedCategories = await newsService.getCategories();
      console.log('🏷️ Categories fetched in useNewsCategories:', fetchedCategories.length, 'categories');
      console.log('🏷️ First few categories:', fetchedCategories.slice(0, 3).map(c => ({ id: c.id, name: c.name, slug: c.slug })));
      setCategories(fetchedCategories);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Có lỗi xảy ra khi tải danh mục');
      console.error('Error fetching categories:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Select a category
   */
  const selectCategory = useCallback((slug: string | undefined) => {
    setSelectedCategory(slug);
  }, []);

  /**
   * Get category by slug
   */
  const getCategoryBySlug = useCallback((slug: string) => {
    return categories.find(category => category.slug === slug);
  }, [categories]);

  /**
   * Get featured categories
   */
  const getFeaturedCategories = useCallback(() => {
    return categories.filter(category => category.featured);
  }, [categories]);

  /**
   * Get categories with article count
   */
  const getCategoriesWithCount = useCallback(() => {
    return categories.filter(category => category.articleCount > 0);
  }, [categories]);

  /**
   * Search categories by name
   */
  const searchCategories = useCallback((query: string) => {
    if (!query.trim()) {
      return categories;
    }

    const normalizedQuery = query.toLowerCase().trim();
    return categories.filter(category =>
      category.name.toLowerCase().includes(normalizedQuery) ||
      category.description?.toLowerCase().includes(normalizedQuery)
    );
  }, [categories]);

  /**
   * Sort categories by different criteria
   */
  const sortCategories = useCallback((
    sortBy: 'name' | 'articleCount' | 'order' = 'order',
    direction: 'asc' | 'desc' = 'asc'
  ) => {
    const sorted = [...categories].sort((a, b) => {
      let aValue: any;
      let bValue: any;

      switch (sortBy) {
        case 'name':
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
          break;
        case 'articleCount':
          aValue = a.articleCount;
          bValue = b.articleCount;
          break;
        case 'order':
        default:
          aValue = a.order;
          bValue = b.order;
          break;
      }

      if (direction === 'desc') {
        return aValue < bValue ? 1 : aValue > bValue ? -1 : 0;
      } else {
        return aValue > bValue ? 1 : aValue < bValue ? -1 : 0;
      }
    });

    setCategories(sorted);
  }, [categories]);

  /**
   * Refresh categories
   */
  const refresh = useCallback(() => {
    fetchCategories();
  }, [fetchCategories]);

  /**
   * Add a new category (for admin use)
   */
  const addCategory = useCallback((category: NewsCategory) => {
    setCategories(prev => [...prev, category]);
  }, []);

  /**
   * Update a category (for admin use)
   */
  const updateCategory = useCallback((categoryId: string, updates: Partial<NewsCategory>) => {
    setCategories(prev =>
      prev.map(category =>
        category.id === categoryId
          ? { ...category, ...updates }
          : category
      )
    );
  }, []);

  /**
   * Remove a category (for admin use)
   */
  const removeCategory = useCallback((categoryId: string) => {
    setCategories(prev => prev.filter(category => category.id !== categoryId));
  }, []);

  /**
   * Get category statistics
   */
  const getStatistics = useCallback(() => {
    const totalCategories = categories.length;
    const featuredCategories = categories.filter(cat => cat.featured).length;
    const totalArticles = categories.reduce((sum, cat) => sum + cat.articleCount, 0);
    const averageArticlesPerCategory = totalCategories > 0 ? totalArticles / totalCategories : 0;

    return {
      totalCategories,
      featuredCategories,
      totalArticles,
      averageArticlesPerCategory
    };
  }, [categories]);

  // Auto-fetch categories on mount
  useEffect(() => {
    console.log('🎯 useNewsCategories useEffect triggered, autoFetch:', autoFetch);
    if (autoFetch) {
      console.log('🎯 Starting fetchCategories...');

      // Call API directly to test
      const fetchCategoriesNow = async () => {
        try {
          setLoading(true);
          setError(null);
          console.log('🎯 Calling API directly...');

          const response = await fetch('/api/news/categories');
          console.log('🎯 API response status:', response.status);

          if (!response.ok) {
            throw new Error(`API error: ${response.status}`);
          }

          const data = await response.json();
          console.log('🎯 API response data:', data);

          const fetchedCategories = data.data || [];
          console.log('🏷️ Categories fetched in useNewsCategories:', fetchedCategories.length, 'categories');
          console.log('🏷️ First few categories:', fetchedCategories.slice(0, 3).map(c => ({ id: c.id, name: c.name, slug: c.slug })));
          setCategories(fetchedCategories);
        } catch (err) {
          setError(err instanceof Error ? err.message : 'Có lỗi xảy ra khi tải danh mục');
          console.error('🎯 Error fetching categories:', err);
        } finally {
          setLoading(false);
        }
      };

      fetchCategoriesNow();
    } else {
      console.log('🎯 autoFetch is false, skipping fetchCategories');
    }
  }, [autoFetch]);

  return {
    categories,
    loading,
    error,
    selectedCategory,
    selectCategory,
    fetchCategories,
    getCategoryBySlug,
    getFeaturedCategories,
    getCategoriesWithCount,
    searchCategories,
    sortCategories,
    refresh,
    addCategory,
    updateCategory,
    removeCategory,
    getStatistics
  };
}
