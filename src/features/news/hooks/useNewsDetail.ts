// useNewsDetail Hook
// Custom hook for managing news article detail

'use client';

import { useState, useEffect, useCallback } from 'react';
import { NewsService } from '../services/NewsService';
import { NewsArticle } from '../types';

interface UseNewsDetailReturn {
  article: NewsArticle | null;
  relatedArticles: NewsArticle[];
  loading: boolean;
  error: string | null;
  fetchArticle: (slug: string) => Promise<void>;
  fetchRelatedArticles: (slug: string, limit?: number) => Promise<void>;
  incrementViewCount: () => Promise<void>;
  incrementShareCount: () => Promise<void>;
  refresh: () => Promise<void>;
}

/**
 * Custom hook for managing news article detail
 * @param slug - Article slug
 * @param autoFetch - Whether to automatically fetch article on mount
 * @returns Article detail data and management functions
 */
export function useNewsDetail(
  slug?: string,
  autoFetch: boolean = true
): UseNewsDetailReturn {
  const [article, setArticle] = useState<NewsArticle | null>(null);
  const [relatedArticles, setRelatedArticles] = useState<NewsArticle[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * Fetch article by slug
   */
  const fetchArticle = useCallback(async (articleSlug: string) => {
    try {
      setLoading(true);
      setError(null);

      const newsService = NewsService.getInstance();
      const fetchedArticle = await newsService.getArticleBySlug(articleSlug);
      setArticle(fetchedArticle);

      // Auto-fetch related articles
      fetchRelatedArticles(articleSlug);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Có lỗi xảy ra khi tải bài viết');
      console.error('Error fetching article:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Fetch related articles
   */
  const fetchRelatedArticles = useCallback(async (articleSlug: string, limit: number = 5) => {
    try {
      const newsService = NewsService.getInstance();
      const related = await newsService.getRelatedArticles(articleSlug, limit);
      setRelatedArticles(related);
    } catch (err) {
      console.error('Error fetching related articles:', err);
      // Don't set error for related articles as it's not critical
    }
  }, []);

  /**
   * Increment view count
   */
  const incrementViewCount = useCallback(async () => {
    if (!article) return;

    try {
      await NewsService.incrementViewCount(article.id);

      // Update local state
      setArticle(prev => prev ? {
        ...prev,
        viewCount: prev.viewCount + 1
      } : null);
    } catch (err) {
      console.error('Error incrementing view count:', err);
      // Don't show error to user for analytics
    }
  }, [article]);

  /**
   * Increment share count
   */
  const incrementShareCount = useCallback(async () => {
    if (!article) return;

    try {
      await NewsService.incrementShareCount(article.id);

      // Update local state
      setArticle(prev => prev ? {
        ...prev,
        shareCount: prev.shareCount + 1
      } : null);
    } catch (err) {
      console.error('Error incrementing share count:', err);
      // Don't show error to user for analytics
    }
  }, [article]);

  /**
   * Refresh article data
   */
  const refresh = useCallback(async () => {
    if (article?.slug) {
      await fetchArticle(article.slug);
    }
  }, [article?.slug, fetchArticle]);

  /**
   * Share article
   */
  const shareArticle = useCallback(async (platform: 'facebook' | 'twitter' | 'linkedin' | 'copy') => {
    if (!article) return;

    const url = `${window.location.origin}/news/${article.slug}`;
    const title = article.title;
    const description = article.excerpt;

    let shareUrl = '';

    switch (platform) {
      case 'facebook':
        shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`;
        break;
      case 'twitter':
        shareUrl = `https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}&text=${encodeURIComponent(title)}`;
        break;
      case 'linkedin':
        shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`;
        break;
      case 'copy':
        try {
          await navigator.clipboard.writeText(url);
          // You might want to show a toast notification here
          console.log('URL copied to clipboard');
        } catch (err) {
          console.error('Failed to copy URL:', err);
        }
        break;
    }

    if (shareUrl) {
      window.open(shareUrl, '_blank', 'width=600,height=400');
    }

    // Increment share count
    await incrementShareCount();
  }, [article, incrementShareCount]);

  /**
   * Get reading progress
   */
  const getReadingProgress = useCallback((scrollPosition: number, contentHeight: number) => {
    if (!contentHeight) return 0;

    const progress = Math.min(100, Math.max(0, (scrollPosition / contentHeight) * 100));
    return Math.round(progress);
  }, []);

  /**
   * Mark article as read (for user analytics)
   */
  const markAsRead = useCallback(() => {
    if (!article) return;

    // Store in localStorage for user reading history
    const readArticles = JSON.parse(localStorage.getItem('readArticles') || '[]');
    const articleData = {
      id: article.id,
      slug: article.slug,
      title: article.title,
      readAt: new Date().toISOString()
    };

    const updatedReadArticles = [
      articleData,
      ...readArticles.filter((a: any) => a.id !== article.id)
    ].slice(0, 100); // Keep only last 100 read articles

    localStorage.setItem('readArticles', JSON.stringify(updatedReadArticles));
  }, [article]);

  // Auto-fetch article on mount or slug change
  useEffect(() => {
    if (autoFetch && slug) {
      fetchArticle(slug);
    }
  }, [autoFetch, slug, fetchArticle]);

  // Increment view count when article is loaded
  useEffect(() => {
    if (article && !loading) {
      // Delay view count increment to ensure user actually views the article
      const timer = setTimeout(() => {
        incrementViewCount();
        markAsRead();
      }, 3000); // 3 seconds delay

      return () => clearTimeout(timer);
    }
  }, [article, loading, incrementViewCount, markAsRead]);

  return {
    article,
    relatedArticles,
    loading,
    error,
    fetchArticle,
    fetchRelatedArticles,
    incrementViewCount,
    incrementShareCount,
    refresh,
    shareArticle,
    getReadingProgress,
    markAsRead
  };
}
