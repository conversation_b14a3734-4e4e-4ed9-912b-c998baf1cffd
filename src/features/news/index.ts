// News Feature Exports
// This file manages all exports for the news feature module

// Configuration
export { newsFeatureConfig, getCurrentNewsConfig, getPageVersion, getComponentVersion } from './config';
export type { NewsFeatureConfig } from './config';

// Types
export * from './types';

// Services
export { NewsService, newsService } from './services/NewsService';

// Pages
export * from './pages';

// Components (when implemented)
// export * from './components';

// Hooks
export * from './hooks';

// Utils
export * from './utils';

// Layouts
export { default as NewsLayout } from './layouts/NewsLayout';
