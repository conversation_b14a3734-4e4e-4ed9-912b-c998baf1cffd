// News Cache Service
// Handles caching for news articles and categories

'use client';

import { NewsArticle, NewsCategory } from '../types';
import type { NewsAPIResponse, NewsCategoriesAPIResponse, NewsAPIParams } from '@/lib/api/news';

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  expiresAt: number;
}

/**
 * News Cache Service
 * Provides caching functionality for news articles and categories
 */
export class NewsCacheService {
  private static instance: NewsCacheService;
  private cache = new Map<string, CacheEntry<any>>();
  private readonly DEFAULT_TTL = 5 * 60 * 1000; // 5 minutes
  private readonly CATEGORIES_TTL = 30 * 60 * 1000; // 30 minutes

  private constructor() { }

  public static getInstance(): NewsCacheService {
    if (!NewsCacheService.instance) {
      NewsCacheService.instance = new NewsCacheService();
    }
    return NewsCacheService.instance;
  }

  /**
   * Generate cache key for articles
   */
  static generateArticlesKey(params: NewsAPIParams): string {
    const sortedParams = Object.keys(params)
      .sort()
      .reduce((result, key) => {
        const value = params[key as keyof NewsAPIParams];
        if (value !== undefined && value !== null) {
          result[key] = value;
        }
        return result;
      }, {} as Record<string, any>);

    return `articles:${JSON.stringify(sortedParams)}`;
  }

  /**
   * Generate cache key for categories
   */
  static generateCategoriesKey(): string {
    return 'categories:all';
  }

  /**
   * Generate cache key for article detail
   */
  static generateArticleKey(slug: string): string {
    return `article:${slug}`;
  }

  /**
   * Generate cache key for related articles
   */
  static generateRelatedKey(slug: string, limit: number): string {
    return `related:${slug}:${limit}`;
  }

  /**
   * Cache articles response
   */
  cacheArticles(key: string, data: NewsAPIResponse, ttl?: number): void {
    const expiresAt = Date.now() + (ttl || this.DEFAULT_TTL);
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      expiresAt
    });
  }

  /**
   * Get cached articles response
   */
  getCachedArticles(key: string): NewsAPIResponse | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    if (Date.now() > entry.expiresAt) {
      this.cache.delete(key);
      return null;
    }

    return entry.data;
  }

  /**
   * Cache categories response
   */
  cacheCategories(data: NewsCategoriesAPIResponse): void {
    const key = NewsCacheService.generateCategoriesKey();
    const expiresAt = Date.now() + this.CATEGORIES_TTL;
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      expiresAt
    });
  }

  /**
   * Get cached categories response
   */
  getCachedCategories(): NewsCategoriesAPIResponse | null {
    const key = NewsCacheService.generateCategoriesKey();
    const entry = this.cache.get(key);
    if (!entry) return null;

    if (Date.now() > entry.expiresAt) {
      this.cache.delete(key);
      return null;
    }

    return entry.data;
  }

  /**
   * Cache single article
   */
  cacheArticle(slug: string, data: NewsArticle, ttl?: number): void {
    const key = NewsCacheService.generateArticleKey(slug);
    const expiresAt = Date.now() + (ttl || this.DEFAULT_TTL);
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      expiresAt
    });
  }

  /**
   * Get cached single article
   */
  getCachedArticle(slug: string): NewsArticle | null {
    const key = NewsCacheService.generateArticleKey(slug);
    const entry = this.cache.get(key);
    if (!entry) return null;

    if (Date.now() > entry.expiresAt) {
      this.cache.delete(key);
      return null;
    }

    return entry.data;
  }

  /**
   * Cache related articles
   */
  cacheRelatedArticles(slug: string, limit: number, data: NewsArticle[], ttl?: number): void {
    const key = NewsCacheService.generateRelatedKey(slug, limit);
    const expiresAt = Date.now() + (ttl || this.DEFAULT_TTL);
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      expiresAt
    });
  }

  /**
   * Get cached related articles
   */
  getCachedRelatedArticles(slug: string, limit: number): NewsArticle[] | null {
    const key = NewsCacheService.generateRelatedKey(slug, limit);
    const entry = this.cache.get(key);
    if (!entry) return null;

    if (Date.now() > entry.expiresAt) {
      this.cache.delete(key);
      return null;
    }

    return entry.data;
  }

  /**
   * Clear all cache
   */
  clearAll(): void {
    this.cache.clear();
  }

  /**
   * Clear expired entries
   */
  clearExpired(): void {
    const now = Date.now();
    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.expiresAt) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * Get cache stats
   */
  getStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    };
  }

  /**
   * Check if key exists and is valid
   */
  has(key: string): boolean {
    const entry = this.cache.get(key);
    if (!entry) return false;

    if (Date.now() > entry.expiresAt) {
      this.cache.delete(key);
      return false;
    }

    return true;
  }
}
