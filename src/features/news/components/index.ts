// News Components Exports
// This file manages all component exports for the news feature

import { newsFeatureConfig } from '../config';

// Component version management
const getNewsCard = () => {
  const version = newsFeatureConfig.components.newsCard;
  return import(`./news-card/v${version}`).then(m => m.default);
};

const getCategoryFilter = () => {
  const version = newsFeatureConfig.components.categoryFilter;
  return import(`./category-filter/v${version}`).then(m => m.default);
};

const getSearchBar = () => {
  const version = newsFeatureConfig.components.searchBar;
  return import(`./search-bar/v${version}`).then(m => m.default);
};

const getNewsGrid = () => {
  const version = newsFeatureConfig.components.newsGrid;
  return import(`./news-grid/v${version}`).then(m => m.default);
};

const getNewsHero = () => {
  const version = newsFeatureConfig.components.newsHero;
  return import(`./news-hero/v${version}`).then(m => m.default);
};

const getCategorySidebar = () => {
  const version = newsFeatureConfig.components.categorySidebar;
  return import(`./category-sidebar/v${version}`).then(m => m.default);
};

const getNewsList = () => {
  const version = newsFeatureConfig.components.newsList;
  return import(`./news-list/v${version}`).then(m => m.default);
};

const getPagination = () => {
  const version = newsFeatureConfig.components.pagination;
  return import(`./pagination/v${version}`).then(m => m.default);
};

// Static imports for current versions
export { default as NewsCard } from './news-card';
export { default as CategoryFilter } from './category-filter';
export { default as CategorySidebar } from './category-sidebar';
export { default as NewsList } from './news-list';
export { default as SearchBar } from './search-bar';
export { default as NewsGrid } from './news-grid';
export { default as NewsHero } from './news-hero';
export { default as Pagination } from './pagination';

// Export dynamic loaders
export {
  getNewsCard,
  getCategoryFilter,
  getCategorySidebar,
  getNewsList,
  getSearchBar,
  getNewsGrid,
  getNewsHero,
  getPagination
};

// Re-export all component versions
export * from './news-card';
export * from './category-filter';
export * from './category-sidebar';
export * from './news-list';
export * from './search-bar';
export * from './news-grid';
export * from './news-hero';
export * from './pagination';
