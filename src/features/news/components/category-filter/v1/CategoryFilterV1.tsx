// Category Filter V1 Component
'use client';

import React from 'react';
import { CategoryFilterProps } from '../types';

/**
 * Category Filter V1 - Basic category filter component
 * @version 1.0.0
 * @since 2024-01-25
 */
export default function CategoryFilterV1({
  categories,
  selectedCategory,
  onCategoryChange,
  showAll = true,
  variant = 'sidebar',
  className = ''
}: CategoryFilterProps) {
  // Debug log
  console.log('🏷️ CategoryFilter received categories:', categories?.length || 0, 'categories');
  console.log('🏷️ Categories data:', categories?.slice(0, 3).map(c => ({
    id: c?.id,
    name: c?.name,
    slug: c?.slug,
    articleCount: c?.articleCount
  })) || []);
  const getVariantStyles = () => {
    switch (variant) {
      case 'horizontal':
        return {
          container: 'flex flex-wrap gap-2',
          item: 'px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200',
          itemActive: 'bg-blue-600 text-white',
          itemInactive: 'bg-gray-100 text-gray-700 hover:bg-gray-200'
        };
      case 'dropdown':
        return {
          container: 'relative',
          item: 'block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100',
          itemActive: 'bg-blue-50 text-blue-700',
          itemInactive: 'text-gray-700'
        };
      default: // sidebar
        return {
          container: 'space-y-1',
          item: 'flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200',
          itemActive: 'bg-blue-100 text-blue-700 border-l-4 border-blue-600',
          itemInactive: 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
        };
    }
  };

  const styles = getVariantStyles();

  const handleCategoryClick = (categorySlug?: string) => {
    onCategoryChange(categorySlug);
  };

  if (variant === 'dropdown') {
    return (
      <div className={`${styles.container} ${className}`}>
        <select
          value={selectedCategory || ''}
          onChange={(e) => handleCategoryClick(e.target.value || undefined)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          {showAll && (
            <option value="">Tất cả danh mục</option>
          )}
          {categories?.filter(category => category && category.id).map((category) => (
            <option key={category.id} value={category.slug}>
              {category.name || 'Unknown Category'} ({category.articleCount || 0})
            </option>
          )) || []}
        </select>
      </div>
    );
  }

  return (
    <div className={`${styles.container} ${className}`}>
      {variant === 'sidebar' && (
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Danh mục</h3>
      )}

      {showAll && (
        <button
          onClick={() => handleCategoryClick(undefined)}
          className={`${styles.item} ${!selectedCategory ? styles.itemActive : styles.itemInactive
            } w-full text-left`}
        >
          <span className="flex items-center justify-between">
            <span>Tất cả tin tức</span>
            <span className="text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded-full">
              {categories?.reduce((total, cat) => total + (cat?.articleCount || 0), 0) || 0}
            </span>
          </span>
        </button>
      )}

      {categories?.filter(category => category && category.id).map((category) => (
        <button
          key={category.id}
          onClick={() => handleCategoryClick(category.slug)}
          className={`${styles.item} ${selectedCategory === category.slug ? styles.itemActive : styles.itemInactive
            } w-full text-left`}
        >
          <span className="flex items-center justify-between">
            <span className="flex items-center">
              {category.color && (
                <span
                  className="w-3 h-3 rounded-full mr-2"
                  style={{ backgroundColor: category.color }}
                />
              )}
              {category.name || 'Unknown Category'}
            </span>
            <span className="text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded-full">
              {category.articleCount || 0}
            </span>
          </span>
        </button>
      )) || []}
    </div>
  );
}
