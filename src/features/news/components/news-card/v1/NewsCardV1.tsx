// News Card V1 Component
'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { NewsCardProps } from '../types';
import { formatDistanceToNow } from 'date-fns';
import { vi } from 'date-fns/locale';

/**
 * News Card V1 - Basic news card component
 * @version 1.0.0
 * @since 2024-01-25
 */
export default function NewsCardV1({
  article,
  variant = 'default',
  showCategory = true,
  showAuthor = true,
  showDate = true,
  showExcerpt = true,
  showReadingTime = true,
  showImage = true,
  onClick,
  className = ''
}: NewsCardProps) {
  const handleClick = (e: React.MouseEvent) => {
    if (onClick) {
      e.preventDefault();
      onClick(article);
    }
  };

  const getVariantStyles = () => {
    switch (variant) {
      case 'featured':
        return {
          container: 'bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300 border border-gray-100',
          image: 'h-64 w-full object-cover',
          content: 'p-6',
          title: 'text-2xl font-bold text-gray-900 mb-3 line-clamp-2',
          excerpt: 'text-gray-600 mb-4 line-clamp-3',
          meta: 'flex items-center justify-between text-sm text-gray-500'
        };
      case 'compact':
        return {
          container: 'bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow duration-200 border border-gray-100',
          image: 'h-32 w-full object-cover',
          content: 'p-4',
          title: 'text-lg font-semibold text-gray-900 mb-2 line-clamp-2',
          excerpt: 'text-gray-600 mb-3 line-clamp-2 text-sm',
          meta: 'flex items-center justify-between text-xs text-gray-500'
        };
      case 'minimal':
        return {
          container: 'bg-white rounded-lg overflow-hidden hover:bg-gray-50 transition-colors duration-200',
          image: 'h-24 w-full object-cover',
          content: 'p-3',
          title: 'text-base font-medium text-gray-900 mb-1 line-clamp-2',
          excerpt: 'text-gray-600 mb-2 line-clamp-1 text-sm',
          meta: 'flex items-center text-xs text-gray-500'
        };
      default:
        return {
          container: 'bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300 border border-gray-100',
          image: 'h-48 w-full object-cover',
          content: 'p-5',
          title: 'text-xl font-semibold text-gray-900 mb-2 line-clamp-2',
          excerpt: 'text-gray-600 mb-4 line-clamp-3',
          meta: 'flex items-center justify-between text-sm text-gray-500'
        };
    }
  };

  const styles = getVariantStyles();
  // Use direct URL from backend - no proxy needed
  const imageUrl = article.featuredImage || 'https://via.placeholder.com/400x200/f3f4f6/9ca3af?text=News+Image';

  return (
    <Link href={`/news/${article.slug}`} className={`block ${className}`}>
      <article className={styles.container} onClick={handleClick}>
        {showImage && article.featuredImage && (
          <div className="relative">
            <Image
              src={imageUrl}
              alt={article.title}
              width={400}
              height={200}
              className={styles.image}
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.src = '/images/placeholder-news.jpg';
              }}
            />
            {showCategory && (
              <div className="absolute top-3 left-3">
                <span
                  className="px-3 py-1 text-xs font-medium text-white rounded-full"
                  style={{ backgroundColor: article.category?.color || '#3B82F6' }}
                >
                  {article.category?.name || 'Uncategorized'}
                </span>
              </div>
            )}
          </div>
        )}

        <div className={styles.content}>
          {!showImage && showCategory && (
            <div className="mb-2">
              <span
                className="px-2 py-1 text-xs font-medium text-white rounded"
                style={{ backgroundColor: article.category?.color || '#3B82F6' }}
              >
                {article.category?.name || 'Uncategorized'}
              </span>
            </div>
          )}

          <h3 className={styles.title}>
            {article.title}
          </h3>

          {showExcerpt && article.excerpt && (
            <p className={styles.excerpt}>
              {article.excerpt}
            </p>
          )}

          <div className={styles.meta}>
            <div className="flex items-center space-x-3">
              {showAuthor && (
                <span className="flex items-center">
                  <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                  </svg>
                  {article.author?.name || 'Unknown Author'}
                </span>
              )}

              {showDate && (
                <span className="flex items-center">
                  <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                  </svg>
                  {formatDistanceToNow(new Date(article.publishedAt), {
                    addSuffix: true,
                    locale: vi
                  })}
                </span>
              )}
            </div>

            {showReadingTime && (
              <span className="flex items-center">
                <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                </svg>
                {article.readingTime || 5} phút đọc
              </span>
            )}
          </div>
        </div>
      </article>
    </Link>
  );
}
