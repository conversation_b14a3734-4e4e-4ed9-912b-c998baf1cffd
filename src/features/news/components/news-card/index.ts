// News Card Component Version Management
// Change the import to switch between versions easily

// Available Versions:
// import NewsCard from './v1';  // Initial Implementation

// Current Active Version
export { default } from './v1';  // Initial Implementation
export { default as NewsCard } from './v1';  // Named export for compatibility

// Dynamic import based on current version
const getNewsCard = () => {
  return import('./v1').then(m => m.default);
};

// Export version loader
export { getNewsCard };

// Re-export types
export * from './types';
