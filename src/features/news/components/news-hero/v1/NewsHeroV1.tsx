// News Hero V1 Component
'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { NewsHeroProps } from '../types';
import { SearchBar } from '../../search-bar';
import { getImageUrl } from '../../../utils';
import { formatDistanceToNow } from 'date-fns';
import { vi } from 'date-fns/locale';

/**
 * News Hero V1 - Hero section for news pages
 * @version 1.0.0
 * @since 2024-01-25
 */
export default function NewsHeroV1({
  featuredArticle,
  recentArticles = [],
  loading = false,
  error = null,
  variant = 'default',
  showSearch = true,
  searchProps,
  onArticleClick,
  className = '',
  backgroundImage,
  overlay = true,
  height = 'lg'
}: NewsHeroProps) {
  const getHeightStyles = () => {
    switch (height) {
      case 'sm': return 'h-64 md:h-80';
      case 'md': return 'h-80 md:h-96';
      case 'lg': return 'h-96 md:h-[32rem]';
      case 'xl': return 'h-[32rem] md:h-[40rem]';
      default: return 'h-96 md:h-[32rem]';
    }
  };

  const handleArticleClick = (article: any) => {
    if (onArticleClick) {
      onArticleClick(article);
    }
  };

  if (loading) {
    return (
      <div className={`relative ${getHeightStyles()} bg-gray-200 animate-pulse ${className}`}>
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
        <div className="relative h-full flex items-end">
          <div className="container mx-auto px-4 pb-8">
            <div className="max-w-4xl">
              <div className="bg-gray-300 rounded h-8 w-32 mb-4"></div>
              <div className="bg-gray-300 rounded h-12 w-3/4 mb-4"></div>
              <div className="bg-gray-300 rounded h-6 w-1/2 mb-6"></div>
              {showSearch && (
                <div className="bg-gray-300 rounded-full h-12 w-full max-w-md"></div>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`relative ${getHeightStyles()} bg-gray-100 flex items-center justify-center ${className}`}>
        <div className="text-center">
          <div className="text-red-500 mb-4">
            <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Không thể tải nội dung</h3>
          <p className="text-gray-600">{error}</p>
        </div>
      </div>
    );
  }

  // Use direct URL from backend - no proxy needed
  const heroImage = featuredArticle?.featuredImage || backgroundImage || '/images/news-hero-bg.jpg';

  if (variant === 'split' && featuredArticle) {
    return (
      <div className={`relative ${getHeightStyles()} ${className}`}>
        <div className="grid grid-cols-1 lg:grid-cols-2 h-full">
          {/* Left Side - Featured Article */}
          <div className="relative">
            <Image
              src={heroImage}
              alt={featuredArticle.title}
              fill
              className="object-cover"
              priority
            />
            {overlay && <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />}

            <div className="absolute inset-0 flex items-end">
              <div className="p-6 text-white">
                <span className="inline-block px-3 py-1 text-xs font-medium bg-blue-600 rounded-full mb-3">
                  {featuredArticle.category.name}
                </span>
                <h1 className="text-2xl md:text-3xl font-bold mb-3 line-clamp-3">
                  {featuredArticle.title}
                </h1>
                <p className="text-gray-200 mb-4 line-clamp-2">
                  {featuredArticle.excerpt}
                </p>
                <div className="flex items-center text-sm text-gray-300">
                  <span>{featuredArticle.author.name}</span>
                  <span className="mx-2">•</span>
                  <span>
                    {formatDistanceToNow(new Date(featuredArticle.publishedAt), {
                      addSuffix: true,
                      locale: vi
                    })}
                  </span>
                </div>
              </div>
            </div>

            <Link
              href={`/news/${featuredArticle.slug}`}
              className="absolute inset-0"
              onClick={() => handleArticleClick(featuredArticle)}
            />
          </div>

          {/* Right Side - Search & Recent Articles */}
          <div className="bg-white p-6 flex flex-col justify-center">
            {showSearch && searchProps && (
              <div className="mb-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">Tìm kiếm tin tức</h2>
                <SearchBar
                  {...searchProps}
                  variant="hero"
                  placeholder="Nhập từ khóa tìm kiếm..."
                />
              </div>
            )}

            {recentArticles.length > 0 && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Tin tức mới nhất</h3>
                <div className="space-y-4">
                  {recentArticles.slice(0, 3).map((article) => (
                    <Link
                      key={article.id}
                      href={`/news/${article.slug}`}
                      className="block group"
                      onClick={() => handleArticleClick(article)}
                    >
                      <div className="flex space-x-3">
                        {article.featuredImage && (
                          <div className="flex-shrink-0">
                            <Image
                              src={article.featuredImage}
                              alt={article.title}
                              width={80}
                              height={60}
                              className="rounded object-cover"
                            />
                          </div>
                        )}
                        <div className="flex-1 min-w-0">
                          <h4 className="text-sm font-medium text-gray-900 group-hover:text-blue-600 line-clamp-2 transition-colors duration-200">
                            {article.title}
                          </h4>
                          <p className="text-xs text-gray-500 mt-1">
                            {formatDistanceToNow(new Date(article.publishedAt), {
                              addSuffix: true,
                              locale: vi
                            })}
                          </p>
                        </div>
                      </div>
                    </Link>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }

  // Default variant
  return (
    <div className={`relative ${getHeightStyles()} ${className}`}>
      {/* Background Image */}
      <Image
        src={heroImage}
        alt={featuredArticle?.title || "News Hero"}
        fill
        className="object-cover"
        priority
      />

      {/* Overlay */}
      {overlay && <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent" />}

      {/* Content */}
      <div className="relative h-full flex items-center">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl text-white">
            {featuredArticle && (
              <>
                <span className="inline-block px-3 py-1 text-xs font-medium bg-blue-600 rounded-full mb-4">
                  {featuredArticle.category.name}
                </span>
                <h1 className="text-3xl md:text-5xl font-bold mb-4 leading-tight">
                  {featuredArticle.title}
                </h1>
                <p className="text-lg md:text-xl text-gray-200 mb-6 line-clamp-3">
                  {featuredArticle.excerpt}
                </p>
                <div className="flex items-center text-sm text-gray-300 mb-8">
                  <span>{featuredArticle.author.name}</span>
                  <span className="mx-2">•</span>
                  <span>
                    {formatDistanceToNow(new Date(featuredArticle.publishedAt), {
                      addSuffix: true,
                      locale: vi
                    })}
                  </span>
                  <span className="mx-2">•</span>
                  <span>{featuredArticle.readingTime} phút đọc</span>
                </div>

                <Link
                  href={`/news/${featuredArticle.slug}`}
                  className="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors duration-200"
                  onClick={() => handleArticleClick(featuredArticle)}
                >
                  Đọc bài viết
                  <svg className="ml-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </Link>
              </>
            )}

            {showSearch && searchProps && !featuredArticle && (
              <div className="text-center">
                <h1 className="text-3xl md:text-5xl font-bold mb-4">Tin tức thể thao</h1>
                <p className="text-lg md:text-xl text-gray-200 mb-8">
                  Cập nhật tin tức thể thao mới nhất và chính xác nhất
                </p>
                <div className="max-w-md mx-auto">
                  <SearchBar
                    {...searchProps}
                    variant="hero"
                    placeholder="Tìm kiếm tin tức..."
                  />
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
