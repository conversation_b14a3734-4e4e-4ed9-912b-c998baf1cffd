// Search Bar Component Version Management
// Change the import to switch between versions easily

// Available Versions:
// import SearchBar from './v1';  // Initial Implementation

// Current Active Version
export { default } from './v1';  // Initial Implementation

// Named exports for compatibility
import SearchBarV1 from './v1';
export { SearchBarV1 as SearchBar };

// Re-export types
export * from './types';
