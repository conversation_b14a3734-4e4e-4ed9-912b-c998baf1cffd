'use client';

import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

// Types
interface Category {
  id: number;
  name: string;
  slug: string;
  description?: string;
  color?: string;
  icon?: string;
  articleCount: number;
  publishedArticleCount: number;
}

interface CategorySidebarProps {
  className?: string;
}

export default function CategorySidebarV1({ className = '' }: CategorySidebarProps) {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const pathname = usePathname();

  // Fetch categories from API
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/news/categories');
        const data = await response.json();
        
        if (data.status === 200 && data.data) {
          setCategories(data.data);
        } else {
          setError('Không thể tải danh sách chuyên mục');
        }
      } catch (err) {
        setError('Lỗi kết nối API');
        console.error('Error fetching categories:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
  }, []);

  // Get current category slug from pathname
  const getCurrentCategorySlug = () => {
    if (pathname.startsWith('/news-category/')) {
      return pathname.split('/news-category/')[1]?.split('?')[0];
    }
    return null;
  };

  const currentCategorySlug = getCurrentCategorySlug();

  if (loading) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border p-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded mb-4"></div>
          {[...Array(5)].map((_, i) => (
            <div key={i} className="h-4 bg-gray-200 rounded mb-3"></div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border p-6 ${className}`}>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Chuyên mục</h3>
        <p className="text-red-500 text-sm">{error}</p>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg shadow-sm border ${className}`}>
      <div className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Chuyên mục</h3>
        
        {/* All News Link */}
        <Link
          href="/news"
          className={`flex items-center justify-between p-3 rounded-lg transition-colors mb-2 ${
            pathname === '/news'
              ? 'bg-blue-50 text-blue-600 border border-blue-200'
              : 'hover:bg-gray-50 text-gray-700'
          }`}
        >
          <div className="flex items-center">
            <div className="w-2 h-2 bg-gray-400 rounded-full mr-3"></div>
            <span className="font-medium">Tất cả tin tức</span>
          </div>
        </Link>

        {/* Categories List */}
        <div className="space-y-2">
          {categories.map((category) => {
            const isActive = currentCategorySlug === category.slug;
            
            return (
              <Link
                key={category.id}
                href={`/news-category/${category.slug}?page=1`}
                className={`flex items-center justify-between p-3 rounded-lg transition-colors ${
                  isActive
                    ? 'bg-blue-50 text-blue-600 border border-blue-200'
                    : 'hover:bg-gray-50 text-gray-700'
                }`}
              >
                <div className="flex items-center">
                  <div 
                    className="w-2 h-2 rounded-full mr-3"
                    style={{ backgroundColor: category.color || '#6B7280' }}
                  ></div>
                  <span className="font-medium">{category.name}</span>
                </div>
                <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                  {category.publishedArticleCount || 0}
                </span>
              </Link>
            );
          })}
        </div>
      </div>
    </div>
  );
}
