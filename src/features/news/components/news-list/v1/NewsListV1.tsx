'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';

// Types
interface NewsArticle {
  id: number;
  title: string;
  slug: string;
  excerpt: string;
  featuredImage?: string;
  publishedAt: string;
  authorId: number;
  category: {
    id: number;
    name: string;
    slug: string;
    color?: string;
  };
  viewCount: number;
  isFeatured: boolean;
}

interface NewsListProps {
  articles: NewsArticle[];
  loading?: boolean;
  className?: string;
}

export default function NewsListV1({ articles, loading = false, className = '' }: NewsListProps) {
  // Format date to Vietnamese format
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('vi-VN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Get author name (mock for now)
  const getAuthorName = (authorId: number) => {
    return `Tác giả ${authorId}`;
  };

  if (loading) {
    return (
      <div className={`space-y-6 ${className}`}>
        {[...Array(5)].map((_, i) => (
          <div key={i} className="bg-white rounded-lg shadow-sm border p-6 animate-pulse">
            <div className="flex gap-4">
              <div className="w-32 h-24 bg-gray-200 rounded-lg flex-shrink-0"></div>
              <div className="flex-1">
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-4 bg-gray-200 rounded mb-2 w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded mb-4 w-1/2"></div>
                <div className="flex gap-4">
                  <div className="h-3 bg-gray-200 rounded w-20"></div>
                  <div className="h-3 bg-gray-200 rounded w-24"></div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (!articles || articles.length === 0) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border p-8 text-center ${className}`}>
        <div className="text-gray-500">
          <svg className="w-16 h-16 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
          </svg>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Không có tin tức</h3>
          <p className="text-gray-500">Hiện tại chưa có tin tức nào trong chuyên mục này.</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {articles.map((article) => (
        <article key={article.id} className="bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow">
          <Link href={`/news/${article.slug}`} className="block p-6">
            <div className="flex gap-4">
              {/* Featured Image */}
              <div className="w-32 h-24 flex-shrink-0">
                {article.featuredImage ? (
                  <Image
                    src={article.featuredImage}
                    alt={article.title}
                    width={128}
                    height={96}
                    className="w-full h-full object-cover rounded-lg"
                  />
                ) : (
                  <div className="w-full h-full bg-gray-200 rounded-lg flex items-center justify-center">
                    <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  </div>
                )}
              </div>

              {/* Content */}
              <div className="flex-1 min-w-0">
                {/* Category Badge */}
                <div className="mb-2">
                  <span 
                    className="inline-block px-2 py-1 text-xs font-medium text-white rounded-full"
                    style={{ backgroundColor: article.category.color || '#6B7280' }}
                  >
                    {article.category.name}
                  </span>
                  {article.isFeatured && (
                    <span className="ml-2 inline-block px-2 py-1 text-xs font-medium text-yellow-800 bg-yellow-100 rounded-full">
                      Nổi bật
                    </span>
                  )}
                </div>

                {/* Title */}
                <h2 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2 hover:text-blue-600 transition-colors">
                  {article.title}
                </h2>

                {/* Excerpt */}
                <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                  {article.excerpt}
                </p>

                {/* Meta Info */}
                <div className="flex items-center text-xs text-gray-500 space-x-4">
                  <span>{formatDate(article.publishedAt)}</span>
                  <span>•</span>
                  <span>{getAuthorName(article.authorId)}</span>
                  <span>•</span>
                  <span>{article.viewCount} lượt xem</span>
                </div>
              </div>
            </div>
          </Link>
        </article>
      ))}
    </div>
  );
}
