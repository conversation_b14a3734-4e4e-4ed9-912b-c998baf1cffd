// Date utilities for news feature
import { formatDistanceToNow as formatDistanceToNowFns } from 'date-fns';

/**
 * Format distance to now using date-fns
 */
export const formatDistanceToNow = (date: string | Date, options?: { addSuffix?: boolean }) => {
  return formatDistanceToNowFns(new Date(date), { addSuffix: true, ...options });
};

/**
 * Format date for display
 */
export const formatDate = (date: string | Date, format: 'short' | 'long' | 'medium' = 'medium') => {
  const dateObj = new Date(date);
  
  switch (format) {
    case 'short':
      return dateObj.toLocaleDateString('vi-VN', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      });
    case 'long':
      return dateObj.toLocaleDateString('vi-VN', {
        weekday: 'long',
        day: 'numeric',
        month: 'long',
        year: 'numeric'
      });
    case 'medium':
    default:
      return dateObj.toLocaleDateString('vi-VN', {
        day: 'numeric',
        month: 'long',
        year: 'numeric'
      });
  }
};

/**
 * Check if date is today
 */
export const isToday = (date: string | Date) => {
  const today = new Date();
  const dateObj = new Date(date);
  
  return today.toDateString() === dateObj.toDateString();
};

/**
 * Check if date is yesterday
 */
export const isYesterday = (date: string | Date) => {
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  const dateObj = new Date(date);
  
  return yesterday.toDateString() === dateObj.toDateString();
};

/**
 * Get relative time string
 */
export const getRelativeTime = (date: string | Date) => {
  if (isToday(date)) {
    return 'Hôm nay';
  }
  
  if (isYesterday(date)) {
    return 'Hôm qua';
  }
  
  return formatDistanceToNow(date);
};
