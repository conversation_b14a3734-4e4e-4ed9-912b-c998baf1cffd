// Breaking News Section Management
// Change the import to switch between versions easily

// Available Versions:
// import BreakingNews from './v1';  // Initial Implementation
// import BreakingNews from './v2';  // Enhanced UI/UX

// Current Active Version
import BreakingNews from './v1';  // Initial Implementation

export default BreakingNews;
export { default as BreakingNews } from './v1';
export { default as BreakingNewsV1 } from './v1';

// Export types
export type {
  NewsItem,
  BreakingNewsData,
  BreakingNewsProps,
  CategoryConfig,
  CategoryType,
  FilterOptions,
  AnimationConfig
} from './v1';

// Version management
export const BREAKING_NEWS_VERSIONS = {
  v1: () => import('./v1').then(m => m.default),
  // v2: () => import('./v2').then(m => m.default),
} as const;

export const VERSION_INFO = {
  v1: { name: 'Initial Implementation', status: 'stable', releaseDate: '2024-01-25' },
  // v2: { name: 'Enhanced UI/UX', status: 'development', releaseDate: '2024-01-30' },
} as const;

export const CURRENT_VERSION = 'v1';

export const getBreakingNewsVersion = () => {
  return CURRENT_VERSION;
};

export const getVersionInfo = (version: string) => {
  return VERSION_INFO[version as keyof typeof VERSION_INFO];
};
