// Breaking News V1 - Initial Implementation
'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { NewsService } from '@/features/news/services/NewsService';
import { NewsArticle } from '@/features/news/types';

// Types
export interface NewsItem {
  id: string;
  title: string;
  excerpt?: string;
  imageUrl?: string;
  category: string;
  publishedAt: string;
  slug: string;
  isFeatured?: boolean;
  priority: number;
  author?: {
    name: string;
  };
}

export interface BreakingNewsData {
  articles: NewsItem[];
  totalCount: number;
}

export interface BreakingNewsProps {
  title?: string;
  maxItems?: number;
  showViewAll?: boolean;
  className?: string;
  variant?: 'default' | 'compact' | 'featured';
}

export interface CategoryConfig {
  [key: string]: {
    color: string;
    icon?: string;
  };
}

export type CategoryType = 'football' | 'basketball' | 'tennis' | 'general';
export type FilterOptions = 'all' | 'featured' | 'recent';
export type AnimationConfig = {
  duration: number;
  delay: number;
};

// Category colors
const categoryColors: CategoryConfig = {
  football: { color: 'bg-green-500' },
  basketball: { color: 'bg-orange-500' },
  tennis: { color: 'bg-blue-500' },
  general: { color: 'bg-gray-500' },
  default: { color: 'bg-blue-600' }
};

// Priority indicators
const priorityIndicator: { [key: number]: string } = {
  1: 'ring-2 ring-red-500',
  2: 'ring-2 ring-orange-500',
  3: 'ring-2 ring-yellow-500',
  4: '',
  5: ''
};

/**
 * Breaking News V1 - Initial Implementation
 * 
 * @version 1.0.0
 * @since 2024-01-25
 * 
 * @description
 * Breaking news section that displays latest news articles with:
 * - Integration with News Feature services
 * - Featured articles highlighting
 * - Category-based styling
 * - Responsive grid layout
 * - Priority-based visual indicators
 * 
 * @features
 * - Real API integration via NewsService
 * - Featured articles with special styling
 * - Category color coding
 * - Priority visual indicators
 * - Responsive design
 * - Loading and error states
 */
export default function BreakingNewsV1({
  title = "Breaking News",
  maxItems = 6,
  showViewAll = true,
  className = "",
  variant = 'default'
}: BreakingNewsProps) {
  const [articles, setArticles] = useState<NewsArticle[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch news articles
  useEffect(() => {
    const fetchNews = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Fetch featured articles for breaking news
        const newsService = new NewsService();
        const featuredArticles = await newsService.getFeaturedArticles(maxItems);
        
        setArticles(featuredArticles);
      } catch (err) {
        console.error('Error fetching breaking news:', err);
        setError('Failed to load breaking news');
      } finally {
        setLoading(false);
      }
    };

    fetchNews();
  }, [maxItems]);

  // Convert NewsArticle to NewsItem format
  const convertToNewsItem = (article: NewsArticle): NewsItem => ({
    id: article.id,
    title: article.title,
    excerpt: article.excerpt,
    imageUrl: article.featuredImage?.url,
    category: article.category.slug,
    publishedAt: article.publishedAt,
    slug: article.slug,
    isFeatured: article.isFeatured,
    priority: article.priority || 5,
    author: article.author
  });

  const displayNews = articles.map(convertToNewsItem);
  const featuredNews = displayNews.filter(item => item.isFeatured);
  const regularNews = displayNews.filter(item => !item.isFeatured);

  if (loading) {
    return (
      <section className={`py-16 bg-gray-50 ${className}`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading breaking news...</p>
          </div>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section className={`py-16 bg-gray-50 ${className}`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <p className="text-red-600">{error}</p>
          </div>
        </div>
      </section>
    );
  }

  if (displayNews.length === 0) {
    return null;
  }

  return (
    <section className={`py-16 bg-gray-50 ${className}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="flex justify-between items-center mb-12">
          <div>
            <h2 className="text-3xl font-bold text-gray-900 flex items-center">
              <span className="w-3 h-3 bg-red-600 rounded-full mr-4 animate-pulse"></span>
              {title}
            </h2>
            <p className="mt-2 text-gray-600">Stay updated with the latest sports news</p>
          </div>
          {showViewAll && (
            <Link 
              href="/news" 
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors duration-200"
            >
              View All News
              <svg className="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </Link>
          )}
        </div>

        {/* Featured News */}
        {featuredNews.length > 0 && (
          <div className="mb-12">
            <h3 className="text-2xl font-semibold text-gray-900 mb-6 flex items-center">
              <span className="w-2 h-2 bg-red-600 rounded-full mr-3"></span>
              Featured Stories
            </h3>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {featuredNews.slice(0, 2).map((newsItem) => (
                <Link key={newsItem.id} href={`/news/${newsItem.slug}`}>
                  <article className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden group">
                    {newsItem.imageUrl && (
                      <div className="relative h-64 overflow-hidden">
                        <Image
                          src={newsItem.imageUrl}
                          alt={newsItem.title}
                          fill
                          className="object-cover group-hover:scale-105 transition-transform duration-300"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                        <div className="absolute bottom-4 left-4 right-4">
                          <span className={`inline-block px-3 py-1 rounded-full text-xs font-medium text-white mb-2 ${categoryColors[newsItem.category]?.color || categoryColors.default.color}`}>
                            {newsItem.category.charAt(0).toUpperCase() + newsItem.category.slice(1)}
                          </span>
                          <h3 className="text-xl font-bold text-white line-clamp-2">
                            {newsItem.title}
                          </h3>
                        </div>
                      </div>
                    )}
                    <div className="p-6">
                      {newsItem.excerpt && (
                        <p className="text-gray-600 line-clamp-2 mb-4">
                          {newsItem.excerpt}
                        </p>
                      )}
                      <div className="flex items-center justify-between text-sm text-gray-500">
                        {newsItem.author && (
                          <span className="flex items-center">
                            <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                            </svg>
                            {newsItem.author.name}
                          </span>
                        )}
                        <time dateTime={newsItem.publishedAt}>
                          {new Date(newsItem.publishedAt).toLocaleDateString()}
                        </time>
                      </div>
                    </div>
                  </article>
                </Link>
              ))}
            </div>
          </div>
        )}

        {/* Regular News Grid */}
        {regularNews.length > 0 && (
          <div className="space-y-6">
            {featuredNews.length > 0 && (
              <h3 className="text-xl font-semibold text-gray-900 flex items-center">
                <span className="w-2 h-2 bg-blue-600 rounded-full mr-3"></span>
                More Stories
              </h3>
            )}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {regularNews.map((newsItem) => (
                <Link key={newsItem.id} href={`/news/${newsItem.slug}`}>
                  <article className={`bg-white rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 overflow-hidden group ${priorityIndicator[newsItem.priority]}`}>
                    {newsItem.imageUrl && (
                      <div className="relative h-48 overflow-hidden">
                        <Image
                          src={newsItem.imageUrl}
                          alt={newsItem.title}
                          fill
                          className="object-cover group-hover:scale-105 transition-transform duration-300"
                        />
                        <span className={`absolute top-3 right-3 px-2 py-1 rounded-full text-xs font-medium text-white ${categoryColors[newsItem.category]?.color || categoryColors.default.color}`}>
                          {newsItem.category.charAt(0).toUpperCase() + newsItem.category.slice(1)}
                        </span>
                      </div>
                    )}
                    <div className="p-4">
                      <h3 className="font-semibold text-gray-900 line-clamp-2 mb-2 group-hover:text-blue-600 transition-colors">
                        {newsItem.title}
                      </h3>
                      {newsItem.excerpt && (
                        <p className="text-gray-600 text-sm line-clamp-2 mb-3">
                          {newsItem.excerpt}
                        </p>
                      )}
                      <div className="flex items-center justify-between text-xs text-gray-500">
                        {newsItem.author && (
                          <span>{newsItem.author.name}</span>
                        )}
                        <time dateTime={newsItem.publishedAt}>
                          {new Date(newsItem.publishedAt).toLocaleDateString()}
                        </time>
                      </div>
                    </div>
                  </article>
                </Link>
              ))}
            </div>
          </div>
        )}
      </div>
    </section>
  );
}
